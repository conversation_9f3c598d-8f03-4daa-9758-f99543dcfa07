/* Bloonsdle Styles */

.game-tile {
    width: 3.5rem;
    height: 3.5rem;
    border: 2px solid #d1d5db;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.game-tile.filled {
    border-color: #9ca3af;
    background-color: white;
}

.game-tile.correct {
    background-color: #7ED321;
    border-color: #7ED321;
    color: white;
}

.game-tile.present {
    background-color: #F5A623;
    border-color: #F5A623;
    color: white;
}

.game-tile.absent {
    background-color: #9B9B9B;
    border-color: #9B9B9B;
    color: white;
}

.game-tile.higher {
    background-color: #D0021B;
    border-color: #D0021B;
    color: white;
}

.game-tile.lower {
    background-color: #4A90E2;
    border-color: #4A90E2;
    color: white;
}

.tower-image {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #4A90E2, #2C5282);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .game-tile {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 0.625rem;
    }
    
    .tower-image {
        width: 24px;
        height: 24px;
        font-size: 8px;
    }
}

/* Animation for new guesses */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.guess-row {
    animation: slideIn 0.3s ease-out;
}

/* Search dropdown styling */
.search-dropdown {
    max-height: 16rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #9ca3af #f3f4f6;
}

.search-dropdown::-webkit-scrollbar {
    width: 6px;
}

.search-dropdown::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
}

.search-dropdown::-webkit-scrollbar-thumb {
    background: #9ca3af;
    border-radius: 3px;
}

.search-dropdown::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Modal backdrop */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

/* Hover effects */
.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* Focus styles */
.focus-ring:focus {
    outline: 2px solid #4A90E2;
    outline-offset: 2px;
}

/* Game end message animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Stats grid styling */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}
