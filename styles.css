/* Bloonsdle Styles */

/* Background */
body {
    background-image: url('img/fondo.png');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 58, 138, 0.85); /* Blue overlay with transparency */
    backdrop-filter: blur(2px);
    z-index: -1;
}

/* Game Mode Cards */
.game-mode-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.game-mode-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.game-mode-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.game-mode-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
}

.game-mode-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    flex-grow: 1;
}

.game-mode-status {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.game-mode-status.completed {
    background: rgba(126, 211, 33, 0.3);
    border-color: rgba(126, 211, 33, 0.5);
}

.game-mode-status.failed {
    background: rgba(208, 2, 27, 0.3);
    border-color: rgba(208, 2, 27, 0.5);
}

.status-text {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.game-tile {
    width: 3.5rem;
    height: 3.5rem;
    border: 2px solid #d1d5db;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.game-tile.filled {
    border-color: #9ca3af;
    background-color: white;
}

.game-tile.correct {
    background-color: #7ED321;
    border-color: #7ED321;
    color: white;
}

.game-tile.present {
    background-color: #F5A623;
    border-color: #F5A623;
    color: white;
}

.game-tile.absent {
    background-color: #9B9B9B;
    border-color: #9B9B9B;
    color: white;
}

.game-tile.higher {
    background-color: #D0021B;
    border-color: #D0021B;
    color: white;
}

.game-tile.lower {
    background-color: #4A90E2;
    border-color: #4A90E2;
    color: white;
}

.tower-image {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #4A90E2, #2C5282);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .game-tile {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 0.625rem;
    }
    
    .tower-image {
        width: 24px;
        height: 24px;
        font-size: 8px;
    }
}

/* Animation for new guesses */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.guess-row {
    animation: slideIn 0.3s ease-out;
}

/* Search dropdown styling */
.search-dropdown {
    max-height: 16rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #9ca3af #f3f4f6;
}

.search-dropdown::-webkit-scrollbar {
    width: 6px;
}

.search-dropdown::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
}

.search-dropdown::-webkit-scrollbar-thumb {
    background: #9ca3af;
    border-radius: 3px;
}

.search-dropdown::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Modal backdrop */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

/* Hover effects */
.hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
}

/* Focus styles */
.focus-ring:focus {
    outline: 2px solid #4A90E2;
    outline-offset: 2px;
}

/* Game end message animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Stats grid styling */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

/* Image standardization */
.game-image {
    width: 32px;
    height: 32px;
    object-fit: cover;
    border-radius: 4px;
}

.game-image-large {
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: 8px;
}

.game-image-hero {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 6px;
}

/* Language selector */
.language-selector {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
}

.language-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.language-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.language-btn.active {
    background: rgba(74, 144, 226, 0.8);
    border-color: rgba(74, 144, 226, 1);
}

/* Reset button */
.reset-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(208, 2, 27, 0.8);
    border: 1px solid rgba(208, 2, 27, 1);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.reset-btn:hover {
    background: rgba(208, 2, 27, 1);
    transform: scale(1.05);
}

/* Image mode specific styles */
.mystery-image {
    transition: all 0.5s ease;
}

.mystery-image.blur-heavy {
    filter: blur(20px) hue-rotate(180deg) saturate(0.3);
    transform: rotate(180deg) scale(0.8);
}

.mystery-image.blur-medium {
    filter: blur(15px) hue-rotate(90deg) saturate(0.6);
    transform: rotate(90deg) scale(0.9);
}

.mystery-image.blur-light {
    filter: blur(10px) hue-rotate(45deg) saturate(0.8);
    transform: rotate(45deg) scale(0.95);
}

.mystery-image.blur-minimal {
    filter: blur(5px) hue-rotate(20deg) saturate(0.9);
    transform: rotate(20deg) scale(0.98);
}

.mystery-image.clear {
    filter: none;
    transform: none;
}
