// Bloonsdle Multi-Mode Game

// Game data
const towers = [
    { id: 'dartMonkey', name: 'Dart Monkey', category: 'Primary', cost: 200, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: [] },
    { id: 'boomerangMonkey', name: 'Boomerang Monkey', category: 'Primary', cost: 325, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 3, special: ['Comeback'] },
    { id: 'bombShooter', name: 'Bomb Shooter', category: 'Primary', cost: 525, damage: 'High', range: 'Medium', attackSpeed: 'Slow', piercing: 999, special: ['Explosive'] },
    { id: 'tackShooter', name: 'Tack Shooter', category: 'Primary', cost: 280, damage: 'Low', range: 'Short', attackSpeed: 'Fast', piercing: 1, special: ['360° Attack'] },
    { id: 'iceMonkey', name: 'Ice Monkey', category: 'Primary', cost: 500, damage: 'Low', range: 'Short', attackSpeed: 'Slow', piercing: 999, special: ['Freeze'] },
    { id: 'glueGunner', name: 'Glue Gunner', category: 'Primary', cost: 275, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Slow'] },
    { id: 'sniperMonkey', name: 'Sniper Monkey', category: 'Military', cost: 350, damage: 'High', range: 'Very Long', attackSpeed: 'Slow', piercing: 2, special: ['Infinite Range'] },
    { id: 'monkeySub', name: 'Monkey Sub', category: 'Military', cost: 325, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 2, special: ['Water Only'] },
    { id: 'monkeyBuccaneer', name: 'Monkey Buccaneer', category: 'Military', cost: 500, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 3, special: ['Water Only'] },
    { id: 'monkeyAce', name: 'Monkey Ace', category: 'Military', cost: 800, damage: 'Medium', range: 'Very Long', attackSpeed: 'Medium', piercing: 4, special: ['Flying'] },
    { id: 'heliPilot', name: 'Heli Pilot', category: 'Military', cost: 1000, damage: 'Medium', range: 'Medium', attackSpeed: 'Fast', piercing: 2, special: ['Flying'] },
    { id: 'mortarMonkey', name: 'Mortar Monkey', category: 'Military', cost: 700, damage: 'High', range: 'Very Long', attackSpeed: 'Slow', piercing: 999, special: ['Explosive'] },
    { id: 'dartlingGunner', name: 'Dartling Gunner', category: 'Military', cost: 850, damage: 'Medium', range: 'Long', attackSpeed: 'Very Fast', piercing: 1, special: ['Manual Targeting'] },
    { id: 'monkeyApprentice', name: 'Wizard Monkey', category: 'Magic', cost: 375, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 2, special: ['Magic'] },
    { id: 'superMonkey', name: 'Super Monkey', category: 'Magic', cost: 2500, damage: 'High', range: 'Long', attackSpeed: 'Very Fast', piercing: 1, special: ['Plasma'] },
    { id: 'ninjaMonkey', name: 'Ninja Monkey', category: 'Magic', cost: 500, damage: 'Low', range: 'Medium', attackSpeed: 'Fast', piercing: 2, special: ['Camo Detection'] },
    { id: 'monkeyAlchemist', name: 'Alchemist', category: 'Magic', cost: 550, damage: 'Medium', range: 'Short', attackSpeed: 'Medium', piercing: 1, special: ['Lead Popping'] },
    { id: 'druidMonkey', name: 'Druid', category: 'Magic', cost: 425, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Nature'] },
    { id: 'bananaFarm', name: 'Banana Farm', category: 'Support', cost: 1150, damage: 'Low', range: 'Short', attackSpeed: 'Slow', piercing: 0, special: ['Income'] },
    { id: 'spikeFactory', name: 'Spike Factory', category: 'Support', cost: 1000, damage: 'Medium', range: 'Short', attackSpeed: 'Slow', piercing: 5, special: ['Spike Placement'] },
    { id: 'monkeyVillage', name: 'Monkey Village', category: 'Support', cost: 1200, damage: 'Low', range: 'Medium', attackSpeed: 'Slow', piercing: 0, special: ['Buff Towers'] },
    { id: 'engineerMonkey', name: 'Engineer Monkey', category: 'Support', cost: 400, damage: 'Low', range: 'Short', attackSpeed: 'Medium', piercing: 2, special: ['Sentries'] },
    { id: 'beastHandler', name: 'Beast Handler', category: 'Support', cost: 800, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Beast Control'] },
    { id: 'merMonkey', name: 'Mermonkey', category: 'Magic', cost: 600, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 2, special: ['Water Only', 'Magic'] }
];

const heroes = [
    {
        id: 'quincy',
        name: 'Quincy',
        category: 'Hero',
        cost: 'Free',
        damage: '540',
        range: 'Long',
        attackSpeed: 'Medium',
        piercing: 2,
        special: ['Camo Detection', 'Bow Mastery'],
        quotes: {
            en: ['Nothing gets past my bow!', 'I am Quincy, son of Quincy!', 'My bow is ready!'],
            es: ['¡Nada pasa mi arco!', '¡Soy Quincy, hijo de Quincy!', '¡Mi arco está listo!']
        }
    },
    {
        id: 'gwendolin',
        name: 'Gwendolin',
        category: 'Hero',
        cost: '725',
        damage: 'Medium',
        range: 'Medium',
        attackSpeed: 'Medium',
        piercing: 1,
        special: ['Fire Damage', 'Heat It Up'],
        quotes: {
            en: ['Time to light it up!', 'Burn baby burn!', 'Feel the heat!'],
            es: ['¡Hora de encenderlo!', '¡Arde bebé arde!', '¡Siente el calor!']
        }
    },
    {
        id: 'strikerJones',
        name: 'Striker Jones',
        category: 'Hero',
        cost: '700',
        damage: 'High',
        range: 'Medium',
        attackSpeed: 'Medium',
        piercing: 1,
        special: ['Explosive', 'Concussive Shell'],
        quotes: {
            en: ['Boom time!', 'Lock and load!', 'Artillery incoming!'],
            es: ['¡Hora de explosión!', '¡Cargar y disparar!', '¡Artillería en camino!']
        }
    },
    {
        id: 'obynGreenFoot',
        name: 'Obyn Greenfoot',
        category: 'Hero',
        cost: '650',
        damage: 'Medium',
        range: 'Medium',
        attackSpeed: 'Medium',
        piercing: 2,
        special: ['Nature Magic', 'Brambles'],
        quotes: {
            en: ['Nature will protect us!', 'The forest spirits guide me!', 'One with nature!'],
            es: ['¡La naturaleza nos protegerá!', '¡Los espíritus del bosque me guían!', '¡Uno con la naturaleza!']
        }
    },
    {
        id: 'captainChurchill',
        name: 'Captain Churchill',
        category: 'Hero',
        cost: '2000',
        damage: 'High',
        range: 'Long',
        attackSpeed: 'Slow',
        piercing: 3,
        special: ['Armor Piercing', 'MOAB Barrage'],
        quotes: {
            en: ['Tank beats everything!', 'Armor up!', 'Heavy artillery ready!'],
            es: ['¡El tanque vence todo!', '¡Blindaje arriba!', '¡Artillería pesada lista!']
        }
    },
    {
        id: 'benjamin',
        name: 'Benjamin',
        category: 'Hero',
        cost: '1200',
        damage: 'Low',
        range: 'Long',
        attackSpeed: 'Slow',
        piercing: 1,
        special: ['Hacking', 'Biohack'],
        quotes: {
            en: ['Hacking the mainframe!', 'Money talks!', 'System compromised!'],
            es: ['¡Hackeando el sistema principal!', '¡El dinero habla!', '¡Sistema comprometido!']
        }
    },
    {
        id: 'ezili',
        name: 'Ezili',
        category: 'Hero',
        cost: '600',
        damage: 'Medium',
        range: 'Medium',
        attackSpeed: 'Medium',
        piercing: 1,
        special: ['Magic', 'MOAB Hex'],
        quotes: {
            en: ['Dark magic flows through me!', 'Cursed be your balloons!', 'Voodoo power!'],
            es: ['¡La magia oscura fluye por mí!', '¡Malditos sean tus globos!', '¡Poder vudú!']
        }
    },
    {
        id: 'patFusty',
        name: 'Pat Fusty',
        category: 'Hero',
        cost: '800',
        damage: 'High',
        range: 'Short',
        attackSpeed: 'Slow',
        piercing: 4,
        special: ['Knockback', 'Rallying Roar'],
        quotes: {
            en: ['Big hugs for everyone!', 'Pat smash!', 'Friendly giant coming through!'],
            es: ['¡Abrazos grandes para todos!', '¡Pat aplasta!', '¡Gigante amigable pasando!']
        }
    },
    {
        id: 'adora',
        name: 'Adora',
        category: 'Hero',
        cost: '1000',
        damage: 'High',
        range: 'Long',
        attackSpeed: 'Fast',
        piercing: 1,
        special: ['Energy Beam', 'Ball of Light'],
        quotes: {
            en: ['The sun god demands sacrifice!', 'Light will purge the darkness!', 'Divine power!'],
            es: ['¡El dios sol exige sacrificio!', '¡La luz purgará la oscuridad!', '¡Poder divino!']
        }
    },
    {
        id: 'admiralBrickell',
        name: 'Admiral Brickell',
        category: 'Hero',
        cost: '900',
        damage: 'Medium',
        range: 'Long',
        attackSpeed: 'Medium',
        piercing: 2,
        special: ['Water Only', 'Naval Tactics'],
        quotes: {
            en: ['Naval superiority!', 'All hands on deck!', 'Anchors aweigh!'],
            es: ['¡Superioridad naval!', '¡Todos a cubierta!', '¡Anclas arriba!']
        }
    },
    {
        id: 'etienne',
        name: 'Etienne',
        category: 'Hero',
        cost: '850',
        damage: 'Low',
        range: 'Long',
        attackSpeed: 'Fast',
        piercing: 1,
        special: ['Camo Detection', 'Drone Swarm'],
        quotes: {
            en: ['Drone squadron ready!', 'Technology is the future!', 'Surveillance online!'],
            es: ['¡Escuadrón de drones listo!', '¡La tecnología es el futuro!', '¡Vigilancia en línea!']
        }
    },
    {
        id: 'sauda',
        name: 'Sauda',
        category: 'Hero',
        cost: '600',
        damage: 'High',
        range: 'Short',
        attackSpeed: 'Fast',
        piercing: 2,
        special: ['Leaping Sword', 'Sword Charge'],
        quotes: {
            en: ['The blade cuts true!', 'Honor guides my sword!', 'Swift and deadly!'],
            es: ['¡La hoja corta certero!', '¡El honor guía mi espada!', '¡Rápida y mortal!']
        }
    },
    {
        id: 'psi',
        name: 'Psi',
        category: 'Hero',
        cost: '1000',
        damage: 'Medium',
        range: 'Medium',
        attackSpeed: 'Medium',
        piercing: 1,
        special: ['Psychic Blast', 'Psionic Scream'],
        quotes: {
            en: ['Mind over matter!', 'Psychic energy flows!', 'The power of thought!'],
            es: ['¡Mente sobre materia!', '¡La energía psíquica fluye!', '¡El poder del pensamiento!']
        }
    },
    {
        id: 'geraldo',
        name: 'Geraldo',
        category: 'Hero',
        cost: '750',
        damage: 'Low',
        range: 'Medium',
        attackSpeed: 'Medium',
        piercing: 1,
        special: ['Shop Items', 'Rejuv Potion'],
        quotes: {
            en: ['I have just the thing!', 'My shop has everything!', 'Quality goods for sale!'],
            es: ['¡Tengo justo lo que necesitas!', '¡Mi tienda tiene de todo!', '¡Productos de calidad en venta!']
        }
    },
    {
        id: 'corvus',
        name: 'Corvus',
        category: 'Hero',
        cost: '1025',
        damage: 'High',
        range: 'Long',
        attackSpeed: 'Medium',
        piercing: 2,
        special: ['Dark Magic', 'Soul Harvest'],
        quotes: {
            en: ['Dark magic awakens!', 'The shadows obey me!', 'Power beyond measure!'],
            es: ['¡La magia oscura despierta!', '¡Las sombras me obedecen!', '¡Poder sin medida!']
        }
    },
    {
        id: 'rosalia',
        name: 'Rosalia',
        category: 'Hero',
        cost: '875',
        damage: 'Medium',
        range: 'Medium',
        attackSpeed: 'Fast',
        piercing: 1,
        special: ['Music Buff', 'Dance Party'],
        quotes: {
            en: ['Let the music play!', 'Dance to victory!', 'Rhythm and power!'],
            es: ['¡Que suene la música!', '¡Baila hacia la victoria!', '¡Ritmo y poder!']
        }
    }
];

const bloons = [
    { id: 'redBloon', name: 'Red Bloon', type: 'Basic', health: 1, speed: 'Slow', special: [] },
    { id: 'blueBloon', name: 'Blue Bloon', type: 'Basic', health: 2, speed: 'Medium', special: [] },
    { id: 'greenBloon', name: 'Green Bloon', type: 'Basic', health: 3, speed: 'Slow', special: [] },
    { id: 'yellowBloon', name: 'Yellow Bloon', type: 'Basic', health: 4, speed: 'Very Fast', special: [] },
    { id: 'pinkBloon', name: 'Pink Bloon', type: 'Basic', health: 5, speed: 'Very Fast', special: [] },
    { id: 'blackBloon', name: 'Black Bloon', type: 'Special', health: 11, speed: 'Medium', special: ['Explosion Immune'] },
    { id: 'whiteBloon', name: 'White Bloon', type: 'Special', health: 11, speed: 'Medium', special: ['Freeze Immune'] },
    { id: 'leadBloon', name: 'Lead Bloon', type: 'Special', health: 23, speed: 'Slow', special: ['Sharp Immune'] },
    { id: 'zebraBloon', name: 'Zebra Bloon', type: 'Special', health: 23, speed: 'Medium', special: ['Explosion Immune', 'Freeze Immune'] },
    { id: 'rainbowBloon', name: 'Rainbow Bloon', type: 'Special', health: 47, speed: 'Fast', special: [] },
    { id: 'ceramicBloon', name: 'Ceramic Bloon', type: 'Special', health: 104, speed: 'Fast', special: ['Extra Tough'] },
    { id: 'purpleBloon', name: 'Purple Bloon', type: 'Special', health: 11, speed: 'Medium', special: ['Magic Immune'] }
];

const moabs = [
    { id: 'redMOAB', name: 'MOAB', type: 'MOAB', health: 616, speed: 'Slow', special: ['Massive'] },
    { id: 'blueMOAB', name: 'BFB', type: 'MOAB', health: 700, speed: 'Slow', special: ['Big', 'Fortified'] },
    { id: 'greenMOAB', name: 'ZOMG', type: 'MOAB', health: 4000, speed: 'Slow', special: ['Zeppelin', 'Massive'] },
    { id: 'purpleMOAB', name: 'DDT', type: 'MOAB', health: 400, speed: 'Slow', special: ['Camo', 'Lead', 'Black Properties'] },
    { id: 'blackMOAB', name: 'BAD', type: 'MOAB', health: 20000, speed: 'Fast', special: ['Boss', 'Immune to Most Abilities'] }
];

const bosses = [
    { id: 'bloonariusBoss', name: 'Bloonarius', type: 'Boss', health: 'Variable', speed: 'Slow', special: ['Spawns Bloons', 'Regenerates'] },
    { id: 'lychBoss', name: 'Lych', type: 'Boss', health: 'Variable', speed: 'Medium', special: ['Steals Buffs', 'Soul Barrier'] },
    { id: 'vortexBoss', name: 'Vortex', type: 'Boss', health: 'Variable', speed: 'Fast', special: ['Stuns Towers', 'Teleports'] },
    { id: 'dreadbloonBoss', name: 'Dreadbloon', type: 'Boss', health: 'Variable', speed: 'Medium', special: ['Rock Armor', 'Spawns Rocks'] },
    { id: 'phayzeBoss', name: 'Phayze', type: 'Boss', health: 'Variable', speed: 'Variable', special: ['Phase Shifts', 'Teleports'] }
];

const towerEmojis = [
    // Torres
    { id: 'dartMonkey', emojis: '🐵🎯', hint: 'Monkey with targeting', type: 'tower' },
    { id: 'boomerangMonkey', emojis: '🪃🔄', hint: 'Returning projectile', type: 'tower' },
    { id: 'bombShooter', emojis: '💣💥', hint: 'Explosive projectiles', type: 'tower' },
    { id: 'tackShooter', emojis: '📌⭐', hint: 'Sharp projectiles in all directions', type: 'tower' },
    { id: 'iceMonkey', emojis: '🧊❄️', hint: 'Freezing attacks', type: 'tower' },
    { id: 'glueGunner', emojis: '🟡🔗', hint: 'Sticky substance', type: 'tower' },
    { id: 'sniperMonkey', emojis: '🔫🎯', hint: 'Long range precision', type: 'tower' },
    { id: 'monkeySub', emojis: '🚢🌊', hint: 'Underwater vessel', type: 'tower' },
    { id: 'monkeyBuccaneer', emojis: '🏴‍☠️⚓', hint: 'Naval warrior', type: 'tower' },
    { id: 'monkeyAce', emojis: '✈️💨', hint: 'Flying machine', type: 'tower' },
    { id: 'heliPilot', emojis: '🚁🌪️', hint: 'Rotating aircraft', type: 'tower' },
    { id: 'mortarMonkey', emojis: '🎯💥', hint: 'Artillery bombardment', type: 'tower' },
    { id: 'dartlingGunner', emojis: '🔫⚡', hint: 'Rapid fire weapon', type: 'tower' },
    { id: 'monkeyApprentice', emojis: '🧙‍♂️✨', hint: 'Magic spells', type: 'tower' },
    { id: 'superMonkey', emojis: '🦸‍♂️⚡', hint: 'Superhero with power', type: 'tower' },
    { id: 'ninjaMonkey', emojis: '🥷⭐', hint: 'Stealthy warrior', type: 'tower' },
    { id: 'monkeyAlchemist', emojis: '🧪⚗️', hint: 'Chemical experiments', type: 'tower' },
    { id: 'druidMonkey', emojis: '🌿🍃', hint: 'Nature magic', type: 'tower' },
    { id: 'bananaFarm', emojis: '🍌🚜', hint: 'Fruit farming', type: 'tower' },
    { id: 'spikeFactory', emojis: '📌🏭', hint: 'Sharp trap production', type: 'tower' },
    { id: 'monkeyVillage', emojis: '🏘️🐵', hint: 'Community support', type: 'tower' },
    { id: 'engineerMonkey', emojis: '🔧⚙️', hint: 'Mechanical constructions', type: 'tower' },
    { id: 'merMonkey', emojis: '🧜‍♂️🌊', hint: 'Aquatic magic user', type: 'tower' },
    { id: 'beastHandler', emojis: '🦁🐾', hint: 'Animal controller', type: 'tower' },

    // Héroes
    { id: 'quincy', emojis: '🏹🎯', hint: 'Archer with perfect aim', type: 'hero' },
    { id: 'gwendolin', emojis: '🔥💥', hint: 'Fire specialist', type: 'hero' },
    { id: 'strikerJones', emojis: '💣🎖️', hint: 'Explosive military expert', type: 'hero' },
    { id: 'obynGreenFoot', emojis: '🌳🍃', hint: 'Forest guardian', type: 'hero' },
    { id: 'captainChurchill', emojis: '🛡️💂', hint: 'Armored leader', type: 'hero' },
    { id: 'benjamin', emojis: '💻💰', hint: 'Tech and money expert', type: 'hero' },
    { id: 'ezili', emojis: '🔮💀', hint: 'Dark magic practitioner', type: 'hero' },
    { id: 'patFusty', emojis: '🦍💪', hint: 'Big friendly giant', type: 'hero' },
    { id: 'adora', emojis: '☀️⚡', hint: 'Sun goddess', type: 'hero' },
    { id: 'admiralBrickell', emojis: '⚓🌊', hint: 'Naval commander', type: 'hero' },
    { id: 'etienne', emojis: '🛸📡', hint: 'Drone operator', type: 'hero' },
    { id: 'sauda', emojis: '⚔️🌸', hint: 'Sword master', type: 'hero' },
    { id: 'psi', emojis: '🧠💫', hint: 'Psychic powers', type: 'hero' },
    { id: 'geraldo', emojis: '🛍️🎪', hint: 'Merchant with gadgets', type: 'hero' },
    { id: 'corvus', emojis: '🌑🔮', hint: 'Dark sorcerer', type: 'hero' },
    { id: 'rosalia', emojis: '🎵💃', hint: 'Musical performer', type: 'hero' }
];

// Current game state
let currentMode = null;
let gameData = {};
let currentLanguage = 'es';

// Game modes
const gameModes = {
    classic: { maxGuesses: 6 },
    quote: { maxGuesses: 3 },
    upgrade: { maxGuesses: 4 },
    emoji: { maxGuesses: 5 },
    image: { maxGuesses: 6 },
    bloons: { maxGuesses: 6 }
};

// Translations
const translations = {
    es: {
        'subtitle': 'Tu dosis de BTD6 diaria',
        'classic-title': 'Clásico',
        'classic-desc': 'Adivina la torre o héroe por sus propiedades',
        'quote-title': 'Frase',
        'quote-desc': 'Adivina el héroe por su frase',
        'upgrade-title': 'Mejora',
        'upgrade-desc': 'Adivina la torre por su mejora',
        'emoji-title': 'Emoji',
        'emoji-desc': 'Adivina por emojis',
        'image-title': 'Imagen',
        'image-desc': 'Adivina por la imagen',
        'bloons-title': 'Globos',
        'bloons-desc': 'Adivina el globo por sus propiedades',
        'play': 'Jugar',
        'completed': '✓ Completado',
        'failed': '✗ Fallido',
        'won': '¡Ganaste!',
        'lost': '¡Perdiste!',
        'correct': '¡Correcto!',
        'incorrect': '¡Incorrecto!',
        'back-menu': 'Volver al Menú',
        'who-said': '¿Qué héroe dijo esta frase?',
        'who-is': '¿Quién o qué es?',
        'hint': 'Pista',
        'tower-was': 'La torre era',
        'hero-was': 'El héroe era',
        'bloon-was': 'El globo era',
        'was': 'Era',
        'image': 'Imagen',
        'name': 'Nombre',
        'category': 'Categoría',
        'cost': 'Coste',
        'damage': 'Daño',
        'range': 'Rango',
        'speed': 'Velocidad',
        'piercing': 'Penetración',
        'special': 'Especial',
        'type': 'Tipo',
        'health': 'Vida',
        'attempts': 'Intentos',
        'placeholder-hero': 'Escribe el nombre del héroe...',
        'placeholder-emoji': 'Escribe el nombre...',
        'placeholder-image': 'Escribe el nombre...',
        'placeholder-tower': 'Escribe el nombre de una torre o héroe...',
        'placeholder-bloon': 'Escribe el nombre de un globo...',
        'classic-title': 'Clásico',
        'classic-desc': 'Adivina la torre por sus propiedades',
        'quote-title': 'Frase',
        'quote-desc': 'Adivina el héroe por su frase',
        'upgrade-title': 'Mejora',
        'upgrade-desc': 'Adivina la torre por su mejora',
        'emoji-title': 'Emoji',
        'emoji-desc': 'Adivina por emojis',
        'image-title': 'Imagen',
        'image-desc': 'Adivina por la imagen',
        'bloons-title': 'Globos',
        'bloons-desc': 'Adivina el globo por sus propiedades',
        'next-reset': 'Próximo reset en:',
        'patch-notes': 'Notas del parche',
        'made-by': 'Hecho por',
        'for-fans': 'para fans de Bloons TD 6'
    },
    en: {
        'subtitle': 'Your daily BTD6 dose',
        'classic-title': 'Classic',
        'classic-desc': 'Guess the tower or hero by properties',
        'quote-title': 'Quote',
        'quote-desc': 'Guess the hero by their quote',
        'upgrade-title': 'Upgrade',
        'upgrade-desc': 'Guess the tower by its upgrade',
        'emoji-title': 'Emoji',
        'emoji-desc': 'Guess by emojis',
        'image-title': 'Image',
        'image-desc': 'Guess by the image',
        'bloons-title': 'Bloons',
        'bloons-desc': 'Guess the bloon by properties',
        'play': 'Play',
        'completed': '✓ Completed',
        'failed': '✗ Failed',
        'won': 'You Won!',
        'lost': 'You Lost!',
        'correct': 'Correct!',
        'incorrect': 'Incorrect!',
        'back-menu': 'Back to Menu',
        'who-said': 'Which hero said this quote?',
        'who-is': 'Who or what is this?',
        'hint': 'Hint',
        'tower-was': 'The tower was',
        'hero-was': 'The hero was',
        'bloon-was': 'The bloon was',
        'was': 'It was',
        'image': 'Image',
        'name': 'Name',
        'category': 'Category',
        'cost': 'Cost',
        'damage': 'Damage',
        'range': 'Range',
        'speed': 'Speed',
        'piercing': 'Piercing',
        'special': 'Special',
        'type': 'Type',
        'health': 'Health',
        'attempts': 'Attempts',
        'placeholder-hero': 'Type the hero name...',
        'placeholder-emoji': 'Type the name...',
        'placeholder-image': 'Type the name...',
        'placeholder-tower': 'Type tower or hero name...',
        'placeholder-bloon': 'Type the bloon name...',
        'classic-title': 'Classic',
        'classic-desc': 'Guess the tower by its properties',
        'quote-title': 'Quote',
        'quote-desc': 'Guess the hero by their quote',
        'upgrade-title': 'Upgrade',
        'upgrade-desc': 'Guess the tower by its upgrade',
        'emoji-title': 'Emoji',
        'emoji-desc': 'Guess by emojis',
        'image-title': 'Image',
        'image-desc': 'Guess by the image',
        'bloons-title': 'Bloons',
        'bloons-desc': 'Guess the bloon by its properties',
        'next-reset': 'Next reset in:',
        'patch-notes': 'Patch Notes',
        'made-by': 'Made by',
        'for-fans': 'for Bloons TD 6 fans'
    }
};

// Tower names in different languages
const towerNames = {
    es: {
        'dartMonkey': 'Mono lanzadardos',
        'boomerangMonkey': 'Mono lanzaboomeranes',
        'bombShooter': 'Tirabombas',
        'tackShooter': 'Tirachatuelas',
        'iceMonkey': 'Mono glacial',
        'glueGunner': 'Mono lanzapegamento',
        'sniperMonkey': 'Mono francotirador',
        'monkeySub': 'Submonino',
        'monkeyBuccaneer': 'Mono pirata',
        'monkeyAce': 'Mono as',
        'heliPilot': 'Helipiloto',
        'mortarMonkey': 'Mono mortero',
        'dartlingGunner': 'Disparador de dardos',
        'monkeyApprentice': 'Mono mago',
        'superMonkey': 'Supermono',
        'ninjaMonkey': 'Mono ninja',
        'alchemist': 'Alquimista',
        'druidMonkey': 'Druida',
        'bananaFarm': 'Platanar',
        'spikeFactory': 'Fábrica de clavos',
        'monkeyVillage': 'Monoaldea',
        'engineerMonkey': 'Mono ingeniero',
        'beastHandler': 'Domador',
        'merMonkey': 'Siremono'
    },
    en: {
        'dartMonkey': 'Dart Monkey',
        'boomerangMonkey': 'Boomerang Monkey',
        'bombShooter': 'Bomb Shooter',
        'tackShooter': 'Tack Shooter',
        'iceMonkey': 'Ice Monkey',
        'glueGunner': 'Glue Gunner',
        'sniperMonkey': 'Sniper Monkey',
        'monkeySub': 'Monkey Sub',
        'monkeyBuccaneer': 'Monkey Buccaneer',
        'monkeyAce': 'Monkey Ace',
        'heliPilot': 'Heli Pilot',
        'mortarMonkey': 'Mortar Monkey',
        'dartlingGunner': 'Dartling Gunner',
        'monkeyApprentice': 'Wizard Monkey',
        'superMonkey': 'Super Monkey',
        'ninjaMonkey': 'Ninja Monkey',
        'monkeyAlchemist': 'Alchemist',
        'druidMonkey': 'Druid',
        'bananaFarm': 'Banana Farm',
        'spikeFactory': 'Spike Factory',
        'monkeyVillage': 'Monkey Village',
        'engineerMonkey': 'Engineer Monkey',
        'beastHandler': 'Beast Handler',
        'merMonkey': 'Mermonkey'
    }
};

// Hero names in different languages (most stay the same)
const heroNames = {
    es: {
        'quincy': 'Quincy',
        'gwendolin': 'Gwendolin',
        'strikerJones': 'Striker Jones',
        'obynGreenFoot': 'Obyn Greenfoot',
        'captainChurchill': 'Capitán Churchill',
        'benjamin': 'Benjamin',
        'ezili': 'Ezili',
        'patFusty': 'Pat Fusty',
        'adora': 'Adora',
        'admiralBrickell': 'Almirante Brickell',
        'etienne': 'Etienne',
        'sauda': 'Sauda',
        'psi': 'Psi',
        'geraldo': 'Geraldo',
        'corvus': 'Corvus',
        'rosalia': 'Rosalia'
    },
    en: {
        'quincy': 'Quincy',
        'gwendolin': 'Gwendolin',
        'strikerJones': 'Striker Jones',
        'obynGreenFoot': 'Obyn Greenfoot',
        'captainChurchill': 'Captain Churchill',
        'benjamin': 'Benjamin',
        'ezili': 'Ezili',
        'patFusty': 'Pat Fusty',
        'adora': 'Adora',
        'admiralBrickell': 'Admiral Brickell',
        'etienne': 'Etienne',
        'sauda': 'Sauda',
        'psi': 'Psi',
        'geraldo': 'Geraldo',
        'corvus': 'Corvus',
        'rosalia': 'Rosalia'
    }
};

const bloonNames = {
    es: {
        'red': 'Rojo',
        'blue': 'Azul',
        'green': 'Verde',
        'yellow': 'Amarillo',
        'pink': 'Rosa',
        'black': 'Negro',
        'white': 'Blanco',
        'lead': 'Emplomado',
        'zebra': 'Cebra',
        'rainbow': 'Arcoíris',
        'ceramic': 'Cerámico',
        'moab': 'MOAB',
        'bfb': 'BFB',
        'zomg': 'ZOMG',
        'bad': 'BAD',
        'ddt': 'DDT',
        'bloonarius': 'Bloonarius',
        'lych': 'Lych',
        'vortex': 'Vortex',
        'phayze': 'Phayze',
        'dreadbloon': 'Dreadbloon'
    },
    en: {
        'red': 'Red',
        'blue': 'Blue',
        'green': 'Green',
        'yellow': 'Yellow',
        'pink': 'Pink',
        'black': 'Black',
        'white': 'White',
        'lead': 'Lead',
        'zebra': 'Zebra',
        'rainbow': 'Rainbow',
        'ceramic': 'Ceramic',
        'moab': 'MOAB',
        'bfb': 'BFB',
        'zomg': 'ZOMG',
        'bad': 'BAD',
        'ddt': 'DDT',
        'bloonarius': 'Bloonarius',
        'lych': 'Lych',
        'vortex': 'Vortex',
        'phayze': 'Phayze',
        'dreadbloon': 'Dreadbloon'
    }
};

// Initialize app
function initApp() {
    loadLanguage();
    setupLanguageSelector();
    setupAutoReset();
    setupCountdownTimer();
    showMainMenu();
    updateMenuStatus();
}

// Language functions
function loadLanguage() {
    const saved = localStorage.getItem('bloonsdle-language');
    if (saved) {
        currentLanguage = saved;
    } else {
        // Default to Spanish
        currentLanguage = 'es';
        localStorage.setItem('bloonsdle-language', 'es');
    }
    updateLanguage();
}

function setupLanguageSelector() {
    const selector = document.getElementById('language-select');
    if (selector) {
        selector.value = currentLanguage;
        selector.addEventListener('change', (e) => {
            currentLanguage = e.target.value;
            localStorage.setItem('bloonsdle-language', currentLanguage);
            updateLanguage();
        });
    }
}

function updateLanguage() {
    const elements = document.querySelectorAll('[data-translate]');
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        if (translations[currentLanguage] && translations[currentLanguage][key]) {
            element.textContent = translations[currentLanguage][key];
        }
    });

    // Update menu status indicators
    updateMenuStatus();

    // Update game subtitle if in a game
    if (currentMode) {
        updateGameSubtitle(currentMode);
    }

    // If we're in a game, re-render it to apply translations
    if (currentMode && gameData[currentMode]) {
        renderGame(currentMode);
    }
}

function updateGameSubtitle(mode) {
    const subtitleElement = document.getElementById('game-subtitle');
    if (subtitleElement) {
        subtitleElement.textContent = getModeDescription(mode);
    }
}

function translate(key) {
    return translations[currentLanguage]?.[key] || key;
}

function getTowerName(towerId) {
    return towerNames[currentLanguage]?.[towerId] || towerId;
}

function getHeroName(heroId) {
    return heroNames[currentLanguage]?.[heroId] || heroId;
}

function getBloonName(bloonId) {
    return bloonNames[currentLanguage]?.[bloonId] || bloonId;
}

// Property translations
const propertyTranslations = {
    es: {
        // Categories
        'Primary': 'Primaria',
        'Military': 'Militar',
        'Magic': 'Magia',
        'Support': 'Apoyo',
        'Hero': 'Héroe',

        // Damage levels
        'Low': 'Bajo',
        'Medium': 'Medio',
        'High': 'Alto',
        'Very High': 'Muy Alto',

        // Range levels
        'Short': 'Corto',
        'Medium': 'Medio',
        'Long': 'Largo',
        'Very Long': 'Muy Largo',
        'Infinite Range': 'Rango Infinito',

        // Attack speed
        'Slow': 'Lento',
        'Medium': 'Medio',
        'Fast': 'Rápido',
        'Very Fast': 'Muy Rápido',

        // Bloon types
        'Basic': 'Básico',
        'Special': 'Especial',
        'MOAB': 'MOAB',
        'Boss': 'Jefe',

        // Special abilities
        'Comeback': 'Regreso',
        'Explosive': 'Explosivo',
        'Freeze': 'Congelar',
        'Slow': 'Ralentizar',
        'Water Only': 'Solo Agua',
        'Flying': 'Volador',
        'Manual Targeting': 'Apuntado Manual',
        'Magic': 'Magia',
        'Plasma': 'Plasma',
        'Camo Detection': 'Detección Camo',
        'Lead Popping': 'Romper Plomo',
        'Nature': 'Naturaleza',
        'Income': 'Ingresos',
        'Spike Placement': 'Colocación Pinchos',
        'Buff Towers': 'Potenciar Torres',
        'Sentries': 'Centinelas',
        'Beast Control': 'Control Bestias',
        'Explosion Immune': 'Inmune Explosión',
        'Freeze Immune': 'Inmune Congelación',
        'Sharp Immune': 'Inmune Punzante',
        'Magic Immune': 'Inmune Magia',
        'Extra Tough': 'Extra Resistente',
        'Massive': 'Masivo',
        'Big': 'Grande',
        'Fortified': 'Fortificado',
        'Zeppelin': 'Zeppelin',
        'Camo': 'Camuflaje',
        'Lead': 'Plomo',
        'Black Properties': 'Propiedades Negras',
        'Boss': 'Jefe',
        'Immune to Most Abilities': 'Inmune a Mayoría Habilidades',
        'Spawns Bloons': 'Genera Globos',
        'Regenerates': 'Regenera',
        'Steals Buffs': 'Roba Mejoras',
        'Soul Barrier': 'Barrera Alma',
        'Stuns Towers': 'Aturde Torres',
        'Teleports': 'Teletransporta',
        'Rock Armor': 'Armadura Roca',
        'Spawns Rocks': 'Genera Rocas',
        'Phase Shifts': 'Cambios Fase',
        'None': 'Ninguno'
    },
    en: {
        // Keep original English values
        'Primary': 'Primary',
        'Military': 'Military',
        'Magic': 'Magic',
        'Support': 'Support',
        'Hero': 'Hero',
        'Low': 'Low',
        'Medium': 'Medium',
        'High': 'High',
        'Very High': 'Very High',
        'Short': 'Short',
        'Long': 'Long',
        'Very Long': 'Very Long',
        'Infinite Range': 'Infinite Range',
        'Slow': 'Slow',
        'Fast': 'Fast',
        'Very Fast': 'Very Fast',
        'Basic': 'Basic',
        'Special': 'Special',
        'MOAB': 'MOAB',
        'Boss': 'Boss',
        'None': 'None'
        // ... all other properties stay the same in English
    }
};

function translateProperty(value) {
    if (!value || value === 'N/A') return value;
    return propertyTranslations[currentLanguage]?.[value] || value;
}

function translateSpecialArray(specialArray) {
    if (!specialArray || specialArray.length === 0) return translateProperty('None');
    return specialArray.map(special => translateProperty(special)).join(', ');
}

// Auto reset at midnight
function setupAutoReset() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const msUntilMidnight = tomorrow.getTime() - now.getTime();

    setTimeout(() => {
        // Reset all games at midnight
        Object.keys(localStorage).forEach(key => {
            if (key.startsWith('bloonsdle-')) {
                localStorage.removeItem(key);
            }
        });

        // Reload page to start fresh
        window.location.reload();
    }, msUntilMidnight);
}



function showMainMenu() {
    document.getElementById('main-menu').classList.remove('hidden');
    document.getElementById('game-screen').classList.add('hidden');
}

function startGame(mode) {
    currentMode = mode;
    document.getElementById('main-menu').classList.add('hidden');
    document.getElementById('game-screen').classList.remove('hidden');
    
    // Update game subtitle only (title is now an image)
    updateGameSubtitle(mode);
    
    initGameData(mode);
    renderGame(mode);
}

function getModeName(mode) {
    const names = {
        classic: 'Clásico',
        quote: 'Frase',
        upgrade: 'Mejora',
        emoji: 'Emoji',
        image: 'Imagen'
    };
    return names[mode] || mode;
}

function getModeDescription(mode) {
    const descriptionKeys = {
        classic: 'classic-desc',
        quote: 'quote-desc',
        upgrade: 'upgrade-desc',
        emoji: 'emoji-desc',
        image: 'image-desc',
        bloons: 'bloons-desc'
    };
    return translate(descriptionKeys[mode]) || '';
}

function initGameData(mode) {
    const today = new Date().toISOString().split('T')[0];
    const saved = localStorage.getItem(`bloonsdle-${mode}`);
    
    if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed.date === today) {
            gameData[mode] = parsed;
            return;
        }
    }
    
    // Create new game
    gameData[mode] = {
        mode: mode,
        date: today,
        guesses: [],
        status: 'playing',
        maxGuesses: 6,
        target: getRandomTarget(mode)
    };
    
    saveGame(mode);
}

function getRandomTarget(mode) {
    const today = new Date().toISOString().split('T')[0];
    let hash = 0;
    for (let i = 0; i < today.length; i++) {
        hash = ((hash << 5) - hash) + today.charCodeAt(i);
    }

    switch (mode) {
        case 'classic':
            return towers[Math.abs(hash) % towers.length];
        case 'quote':
            return heroes[Math.abs(hash + 1) % heroes.length];
        case 'emoji':
            return towerEmojis[Math.abs(hash + 2) % towerEmojis.length];
        case 'image':
            // Combine all entities for image mode
            const allEntities = [...towers, ...heroes, ...bloons, ...moabs, ...bosses];
            return allEntities[Math.abs(hash + 3) % allEntities.length];
        case 'bloons':
            // Combine bloons, moabs, and bosses
            const allBloons = [...bloons, ...moabs, ...bosses];
            return allBloons[Math.abs(hash + 4) % allBloons.length];
        default:
            return towers[0];
    }
}

function renderGame(mode) {
    const content = document.getElementById('game-content');

    switch (mode) {
        case 'classic':
            renderClassicGame();
            break;
        case 'quote':
            renderQuoteGame();
            break;
        case 'emoji':
            renderEmojiGame();
            break;
        case 'image':
            renderImageGame();
            break;
        case 'bloons':
            renderBloonsGame();
            break;
        case 'upgrade':
            content.innerHTML = '<div class="text-white text-center text-xl">🔧 Modo Mejora<br><br>En desarrollo...<br><br><button onclick="goBackToMenu()" class="mt-4 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Volver al Menú</button></div>';
            break;
        default:
            content.innerHTML = '<div class="text-white text-center">Modo en desarrollo</div>';
    }
}

function renderClassicGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    content.innerHTML = `
        <div class="w-full max-w-5xl">
            <div class="grid grid-cols-8 gap-1 mb-2 text-xs font-semibold text-white grid-header">
                <div class="p-2">${translate('image')}</div>
                <div class="p-2">${translate('category')}</div>
                <div class="p-2">${translate('cost')}</div>
                <div class="p-2">${translate('damage')}</div>
                <div class="p-2">${translate('range')}</div>
                <div class="p-2">${translate('speed')}</div>
                <div class="p-2">${translate('piercing')}</div>
                <div class="p-2">${translate('special')}</div>
            </div>
            <div id="classic-guesses"></div>
        </div>

        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="tower-input" placeholder="${translate('placeholder-tower')}"
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="tower-suggestions" class="mt-2"></div>
            </div>
        ` : `
            <div class="mt-8 text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? translate('won') : translate('lost')}</div>
                <div class="mb-4 flex items-center justify-center gap-4">
                    <img src="${getImagePath(game.target)}" alt="${game.target.name}" class="game-image-large">
                    <span>${translate('was')}: <strong>${getEntityName(game.target.id)}</strong></span>
                </div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    ${translate('back-menu')}
                </button>
            </div>
        `}
    `;

    renderClassicGuesses();
    if (game.status === 'playing') {
        setupTowerInput();
    }
}

function renderQuoteGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    const quotes = game.target.quotes[currentLanguage] || game.target.quotes.en;
    const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];

    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="text-3xl mb-4">"${randomQuote}"</div>
            <div class="text-lg">${translate('who-said')}</div>
        </div>

        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="hero-input" placeholder="${translate('placeholder-hero')}"
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="hero-suggestions" class="mt-2"></div>
            </div>

            <!-- Failed guesses -->
            <div class="mt-6 flex flex-wrap justify-center gap-3" id="failed-heroes">
                ${game.guesses.filter(g => !g.correct).map(guess => {
                    const hero = heroes.find(h => h.id === guess.id);
                    return hero ? `
                        <div class="failed-guess rounded-lg p-2">
                            <img src="images/heroes/${hero.id}.png" alt="${hero.name}" class="game-image-hero">
                        </div>
                    ` : '';
                }).join('')}
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? translate('correct') : translate('incorrect')}</div>
                <div class="mb-4 flex items-center justify-center gap-4">
                    <img src="images/heroes/${game.target.id}.png" alt="${game.target.name}" class="game-image-large">
                    <span>${translate('was')}: <strong>${getHeroName(game.target.id)}</strong></span>
                </div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    ${translate('back-menu')}
                </button>
            </div>
        `}
    `;

    if (game.status === 'playing') {
        setupHeroInput();
    }
}

function renderEmojiGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="text-6xl mb-4">${game.target.emojis}</div>
        </div>

        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="emoji-input" placeholder="${translate('placeholder-emoji')}"
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="emoji-suggestions" class="mt-2"></div>
            </div>

            <!-- Failed guesses -->
            <div class="mt-6 flex flex-wrap justify-center gap-3" id="failed-entities">
                ${game.guesses.filter(g => !g.correct).map(guess => {
                    const entity = findEntityById(guess.id);
                    return entity ? `
                        <div class="failed-guess rounded-lg p-2">
                            <img src="${getImagePath(entity)}" alt="${entity.name}" class="game-image-hero">
                        </div>
                    ` : '';
                }).join('')}
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? translate('correct') : translate('incorrect')}</div>
                <div class="mb-4 flex items-center justify-center gap-4">
                    <img src="${getImagePath(findEntityById(game.target.id))}" alt="" class="game-image-large">
                    <span>${translate('was')}: <strong>${getEntityName(game.target.id)}</strong></span>
                </div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    ${translate('back-menu')}
                </button>
            </div>
        `}
    `;

    if (game.status === 'playing') {
        setupEmojiInput();
    }
}

function renderImageGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    const imagePath = getImagePath(game.target);
    const blurClass = getBlurClass(game.guesses.length);

    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="mb-4">
                <img src="${imagePath}" alt="${translate('who-is')}" class="w-48 h-48 mx-auto rounded-lg border-4 border-white/20 mystery-image ${blurClass}">
            </div>
            <div class="text-lg">${translate('who-is')}</div>
            <div class="text-sm mt-2 opacity-75">${translate('attempts')}: ${game.guesses.length}/${game.maxGuesses}</div>
        </div>

        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="image-input" placeholder="${translate('placeholder-image')}"
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="image-suggestions" class="mt-2"></div>
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? translate('correct') : translate('incorrect')}</div>
                <div class="mb-4 flex flex-col items-center justify-center gap-4">
                    <img src="${imagePath}" alt="${game.target.name}" class="w-48 h-48 rounded-lg object-contain border-4 border-white/20">
                    <span>${translate('was')}: <strong>${getEntityName(game.target.id)}</strong></span>
                </div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    ${translate('back-menu')}
                </button>
            </div>
        `}
    `;

    if (game.status === 'playing') {
        setupImageInput();
    }
}

function getBlurClass(guessCount) {
    switch (guessCount) {
        case 0: return 'blur-heavy';
        case 1: return 'blur-medium';
        case 2: return 'blur-light';
        case 3: return 'blur-minimal';
        default: return 'clear';
    }
}

function setupImageInput() {
    const input = document.getElementById('image-input');
    const suggestions = document.getElementById('image-suggestions');

    if (!input || !suggestions) return;

    const allEntities = [...towers, ...heroes, ...bloons, ...moabs, ...bosses];

    // Debounce function to reduce lag
    let timeout;
    input.addEventListener('input', (e) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            const query = e.target.value.toLowerCase();

            if (query.length === 0) {
                suggestions.innerHTML = '';
                return;
            }

            const filtered = allEntities.filter(entity => {
                const entityName = entity.name.toLowerCase();
                const translatedName = getEntityName(entity.id).toLowerCase();
                const englishName = (towerNames.en[entity.id] || heroNames.en[entity.id] || entity.name).toLowerCase();

                return entityName.includes(query) ||
                       translatedName.includes(query) ||
                       englishName.includes(query);
            }).slice(0, 6); // Reduced to 6 for better performance

        suggestions.innerHTML = filtered.map(entity => `
            <button onclick="makeGuessFromInput('${entity.id}')"
                    class="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-left flex items-center gap-3 mb-2">
                <img src="${getImagePath(entity)}" alt="${entity.name}" class="game-image-button">
                <div>
                    <div class="font-medium">${getEntityName(entity.id)}</div>
                    <div class="text-sm opacity-75">${entity.category || entity.type || 'Entity'}</div>
                </div>
            </button>
        `).join('');
        }, 150); // 150ms debounce
    });
}

function renderBloonsGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    content.innerHTML = `
        <div class="w-full max-w-4xl">
            <div class="grid grid-cols-5 gap-1 mb-2 text-xs font-semibold text-white grid-header">
                <div class="p-2">${translate('image')}</div>
                <div class="p-2">${translate('type')}</div>
                <div class="p-2">${translate('health')}</div>
                <div class="p-2">${translate('speed')}</div>
                <div class="p-2">${translate('special')}</div>
            </div>
            <div id="bloons-guesses"></div>
        </div>

        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="bloon-input" placeholder="${translate('placeholder-bloon')}"
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="bloon-suggestions" class="mt-2"></div>
            </div>
        ` : `
            <div class="mt-8 text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? translate('won') : translate('lost')}</div>
                <div class="mb-4">${translate('bloon-was')}: <strong>${game.target.name}</strong></div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    ${translate('back-menu')}
                </button>
            </div>
        `}
    `;

    renderBloonsGuesses();
    if (game.status === 'playing') {
        setupBloonInput();
    }
}

function getImagePath(target) {
    if (towers.find(t => t.id === target.id)) return `images/towers/${target.id}.png`;
    if (heroes.find(h => h.id === target.id)) return `images/heroes/${target.id}.png`;
    if (bloons.find(b => b.id === target.id)) return `images/bloons/${target.id}.png`;
    if (moabs.find(m => m.id === target.id)) return `images/moabs/${target.id}.png`;
    if (bosses.find(b => b.id === target.id)) return `images/bosses/${target.id}.png`;
    return 'images/towers/dartMonkey.png'; // fallback
}

function getTargetName(target) {
    if (target.towerId) {
        const tower = towers.find(t => t.id === target.towerId);
        return tower ? tower.name : 'Unknown';
    }
    if (target.heroId) {
        const hero = heroes.find(h => h.id === target.heroId);
        return hero ? hero.name : 'Unknown';
    }
    return target.name || 'Unknown';
}

function getEntityName(id) {
    const tower = towers.find(t => t.id === id);
    if (tower) return getTowerName(id);

    const hero = heroes.find(h => h.id === id);
    if (hero) return getHeroName(id);

    const bloon = bloons.find(b => b.id === id);
    if (bloon) return getBloonName(id);

    const moab = moabs.find(m => m.id === id);
    if (moab) return getBloonName(id);

    const boss = bosses.find(b => b.id === id);
    if (boss) return getBloonName(id);

    return 'Unknown';
}

function makeGuess(id) {
    const game = gameData[currentMode];
    if (game.status !== 'playing') return;

    let correct = false;

    switch (currentMode) {
        case 'classic':
        case 'quote':
        case 'emoji':
        case 'image':
        case 'bloons':
            correct = id === game.target.id;
            break;
    }

    game.guesses.push({ id, correct });

    if (correct) {
        game.status = 'won';
    } else if (game.guesses.length >= game.maxGuesses) {
        game.status = 'lost';
    }

    saveGame(currentMode);
    renderGame(currentMode);
    updateMenuStatus();
}

function renderBloonsGuesses() {
    const game = gameData[currentMode];
    const container = document.getElementById('bloons-guesses');
    if (!container) return;

    container.innerHTML = '';

    game.guesses.forEach(guess => {
        const guessEntity = findEntityById(guess.id);
        if (!guessEntity) return;

        const row = document.createElement('div');
        row.className = 'grid grid-cols-5 gap-1 mb-1 guess-row';

        const feedback = generateBloonsFeedback(guessEntity, game.target);

        row.innerHTML = `
            <div class="flex items-center justify-center">
                <div class="rounded game-tile ${feedback.image}">
                    <img src="${getImagePath(guessEntity)}" alt="${guessEntity.name}" class="game-image">
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.type}">
                    ${translateProperty(guessEntity.type)}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.health}">
                    ${guessEntity.health}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.speed}">
                    ${translateProperty(guessEntity.speed)}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.special}">
                    ${translateSpecialArray(guessEntity.special) || 'None'}
                </div>
            </div>
        `;

        container.appendChild(row);
    });
}

function generateBloonsFeedback(guess, target) {
    return {
        image: guess.id === target.id ? 'correct' : 'absent',
        type: guess.type === target.type ? 'correct' : 'absent',
        health: guess.health === target.health ? 'correct' : 'absent',
        speed: guess.speed === target.speed ? 'correct' : 'absent',
        special: arraysEqual(guess.special, target.special) ? 'correct' :
                 guess.special.some(s => target.special.includes(s)) ? 'present' : 'absent'
    };
}

function arraysEqual(a, b) {
    if (a.length !== b.length) return false;
    return a.every(val => b.includes(val)) && b.every(val => a.includes(val));
}

function findEntityById(id) {
    return towers.find(t => t.id === id) ||
           heroes.find(h => h.id === id) ||
           bloons.find(b => b.id === id) ||
           moabs.find(m => m.id === id) ||
           bosses.find(b => b.id === id);
}

function setupBloonInput() {
    const input = document.getElementById('bloon-input');
    const suggestions = document.getElementById('bloon-suggestions');

    if (!input || !suggestions) return;

    const allBloons = [...bloons, ...moabs, ...bosses];

    input.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();

        if (query.length === 0) {
            suggestions.innerHTML = '';
            return;
        }

        const filtered = allBloons.filter(bloon =>
            bloon.name.toLowerCase().includes(query)
        ).slice(0, 6);

        suggestions.innerHTML = filtered.map(bloon => `
            <button onclick="makeGuessFromInput('${bloon.id}')"
                    class="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-left flex items-center gap-3 mb-2">
                <img src="${getImagePath(bloon)}" alt="${bloon.name}" class="game-image-button">
                <div>
                    <div class="font-medium">${bloon.name}</div>
                    <div class="text-sm opacity-75">${bloon.type} • ${bloon.health} HP</div>
                </div>
            </button>
        `).join('');
    });
}

function makeGuessFromInput(id) {
    makeGuess(id);

    // Clear different input types
    const inputs = ['bloon-input', 'image-input', 'hero-input', 'emoji-input'];
    const suggestions = ['bloon-suggestions', 'image-suggestions', 'hero-suggestions', 'emoji-suggestions'];

    inputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) input.value = '';
    });

    suggestions.forEach(suggestionId => {
        const suggestion = document.getElementById(suggestionId);
        if (suggestion) suggestion.innerHTML = '';
    });
}

function setupHeroInput() {
    const input = document.getElementById('hero-input');
    const suggestions = document.getElementById('hero-suggestions');

    if (!input || !suggestions) return;

    // Debounce function to reduce lag
    let timeout;
    input.addEventListener('input', (e) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            const query = e.target.value.toLowerCase();

            if (query.length === 0) {
                suggestions.innerHTML = '';
                return;
            }

            const filtered = heroes.filter(hero => {
                const heroName = hero.name.toLowerCase();
                const translatedName = getHeroName(hero.id).toLowerCase();
                const englishName = (heroNames.en[hero.id] || hero.name).toLowerCase();

                return heroName.includes(query) ||
                       translatedName.includes(query) ||
                       englishName.includes(query);
            }).slice(0, 6);

            suggestions.innerHTML = filtered.map(hero => `
                <button onclick="makeGuessFromInput('${hero.id}')"
                        class="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-left flex items-center gap-3 mb-2">
                    <img src="images/heroes/${hero.id}.png" alt="${hero.name}" class="game-image-button">
                    <div>
                        <div class="font-medium">${getHeroName(hero.id)}</div>
                        <div class="text-sm opacity-75">Hero</div>
                    </div>
                </button>
            `).join('');
        }, 150);
    });
}

function setupEmojiInput() {
    const input = document.getElementById('emoji-input');
    const suggestions = document.getElementById('emoji-suggestions');

    if (!input || !suggestions) return;

    const allEntities = [...towers, ...heroes];

    // Debounce function to reduce lag
    let timeout;
    input.addEventListener('input', (e) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            const query = e.target.value.toLowerCase();

            if (query.length === 0) {
                suggestions.innerHTML = '';
                return;
            }

            const filtered = allEntities.filter(entity => {
                const entityName = entity.name.toLowerCase();
                const translatedName = getEntityName(entity.id).toLowerCase();
                const englishName = (towerNames.en[entity.id] || heroNames.en[entity.id] || entity.name).toLowerCase();

                return entityName.includes(query) ||
                       translatedName.includes(query) ||
                       englishName.includes(query);
            }).slice(0, 6);

            suggestions.innerHTML = filtered.map(entity => `
                <button onclick="makeGuessFromInput('${entity.id}')"
                        class="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-left flex items-center gap-3 mb-2">
                    <img src="${getImagePath(entity)}" alt="${entity.name}" class="game-image-button">
                    <div>
                        <div class="font-medium">${getEntityName(entity.id)}</div>
                        <div class="text-sm opacity-75">${entity.category || 'Hero'}</div>
                    </div>
                </button>
            `).join('');
        }, 150);
    });
}

function goBackToMenu() {
    showMainMenu();
}

function saveGame(mode) {
    localStorage.setItem(`bloonsdle-${mode}`, JSON.stringify(gameData[mode]));
}

function updateMenuStatus() {
    // Update menu status indicators
    ['classic', 'quote', 'upgrade', 'emoji', 'image', 'bloons'].forEach(mode => {
        const element = document.getElementById(`${mode}-status`);
        if (!element) return;
        
        const saved = localStorage.getItem(`bloonsdle-${mode}`);
        if (saved) {
            const game = JSON.parse(saved);
            const today = new Date().toISOString().split('T')[0];
            
            if (game.date === today) {
                if (game.status === 'won') {
                    element.className = 'game-mode-status completed';
                    element.innerHTML = `<span class="status-text">${translate('completed')}</span>`;
                } else if (game.status === 'lost') {
                    element.className = 'game-mode-status failed';
                    element.innerHTML = `<span class="status-text">${translate('failed')}</span>`;
                } else {
                    element.className = 'game-mode-status';
                    element.innerHTML = `<span class="status-text">${game.guesses.length}/${game.maxGuesses}</span>`;
                }
                return;
            }
        }
        
        element.className = 'game-mode-status';
        element.innerHTML = `<span class="status-text">${translate('play')}</span>`;
    });
}

// Helper functions for classic mode
function renderClassicGuesses() {
    const game = gameData[currentMode];
    const container = document.getElementById('classic-guesses');
    if (!container) return;

    container.innerHTML = '';

    game.guesses.forEach(guess => {
        const guessEntity = findEntityById(guess.id);
        if (!guessEntity) return;

        const row = document.createElement('div');
        row.className = 'grid grid-cols-8 gap-1 mb-1 guess-row';

        const feedback = generateClassicFeedback(guessEntity, game.target);

        row.innerHTML = `
            <div class="flex items-center justify-center">
                <div class="rounded game-tile ${feedback.image}">
                    <img src="${getImagePath(guessEntity)}" alt="${guessEntity.name}" class="game-image">
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.category}">
                    ${translateProperty(guessEntity.category || 'Hero')}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.cost}">
                    $${guessEntity.cost || 'N/A'}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.damage}">
                    ${translateProperty(guessEntity.damage || 'N/A')}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.range}">
                    ${translateProperty(guessEntity.range || 'N/A')}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.attackSpeed}">
                    ${translateProperty(guessEntity.attackSpeed || 'N/A')}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.piercing}">
                    ${guessEntity.piercing || 'N/A'}
                </div>
            </div>
            <div class="flex items-center justify-center">
                <div class="text-xs font-medium rounded game-tile ${feedback.special}">
                    ${translateSpecialArray(guessEntity.special) || 'None'}
                </div>
            </div>
        `;

        container.appendChild(row);
    });
}

function generateClassicFeedback(guess, target) {
    return {
        image: guess.id === target.id ? 'correct' : 'absent',
        category: guess.category === target.category ? 'correct' : 'absent',
        cost: guess.cost === target.cost ? 'correct' : 'absent',
        damage: guess.damage === target.damage ? 'correct' : 'absent',
        range: guess.range === target.range ? 'correct' : 'absent',
        attackSpeed: guess.attackSpeed === target.attackSpeed ? 'correct' : 'absent',
        piercing: guess.piercing === target.piercing ? 'correct' : 'absent',
        special: arraysEqual(guess.special || [], target.special || []) ? 'correct' :
                 (guess.special || []).some(s => (target.special || []).includes(s)) ? 'present' : 'absent'
    };
}

function setupTowerInput() {
    const input = document.getElementById('tower-input');
    const suggestions = document.getElementById('tower-suggestions');

    if (!input || !suggestions) return;

    const allEntities = [...towers, ...heroes];

    input.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();

        if (query.length === 0) {
            suggestions.innerHTML = '';
            return;
        }

        const filtered = allEntities.filter(entity => {
            const entityName = entity.name.toLowerCase();
            const translatedName = getEntityName(entity.id).toLowerCase();
            const englishName = (towerNames.en[entity.id] || heroNames.en[entity.id] || entity.name).toLowerCase();

            return entityName.includes(query) ||
                   translatedName.includes(query) ||
                   englishName.includes(query);
        }).slice(0, 6);

        suggestions.innerHTML = filtered.map(entity => `
            <button onclick="makeGuessFromInput('${entity.id}')"
                    class="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-left flex items-center gap-3 mb-2">
                <img src="${getImagePath(entity)}" alt="${entity.name}" class="game-image-button">
                <div>
                    <div class="font-medium">${getEntityName(entity.id)}</div>
                    <div class="text-sm opacity-75">${entity.category || 'Hero'} • $${entity.cost || 'N/A'}</div>
                </div>
            </button>
        `).join('');
    });
}



function showHelp() {
    document.getElementById('help-modal').classList.remove('hidden');

    const helpContent = currentLanguage === 'es' ? `
        <div class="space-y-4">
            <div>
                <h3 class="text-lg font-bold text-blue-400 mb-2">🎯 Objetivo General</h3>
                <p>Completa todos los modos de juego diarios. Cada modo tiene un objetivo diferente y número limitado de intentos.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-green-400 mb-2">🏗️ Modo Clásico (6 intentos)</h3>
                <p><strong>Objetivo:</strong> Adivina la torre secreta analizando sus propiedades.</p>
                <p><strong>Cómo jugar:</strong> Escribe nombres de torres o héroes. Cada intento te mostrará qué propiedades coinciden:</p>
                <ul class="list-disc list-inside ml-4 mt-2">
                    <li><span class="bg-green-600 px-2 py-1 rounded text-xs">Verde</span> = Propiedad correcta</li>
                    <li><span class="bg-yellow-600 px-2 py-1 rounded text-xs">Amarillo</span> = Parcialmente correcto</li>
                    <li><span class="bg-gray-600 px-2 py-1 rounded text-xs">Gris</span> = Incorrecto</li>
                </ul>
                <p class="mt-2"><strong>Tip:</strong> Fíjate en la categoría, costo y habilidades especiales para reducir opciones.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-purple-400 mb-2">💬 Modo Frase (3 intentos)</h3>
                <p><strong>Objetivo:</strong> Identifica qué héroe dijo la frase mostrada.</p>
                <p><strong>Cómo jugar:</strong> Lee la frase y escribe el nombre del héroe que crees que la dijo.</p>
                <p><strong>Tip:</strong> Cada héroe tiene frases características que reflejan su personalidad y habilidades.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-yellow-400 mb-2">😄 Modo Emoji (5 intentos)</h3>
                <p><strong>Objetivo:</strong> Adivina la torre o héroe representado por emojis.</p>
                <p><strong>Cómo jugar:</strong> Analiza los emojis e intenta deducir qué torre o héroe representan.</p>
                <p><strong>Tip:</strong> Los emojis suelen relacionarse con el tema, habilidades o apariencia del personaje.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-cyan-400 mb-2">🖼️ Modo Imagen (6 intentos)</h3>
                <p><strong>Objetivo:</strong> Identifica el personaje en la imagen borrosa.</p>
                <p><strong>Cómo jugar:</strong> La imagen se aclara con cada intento fallido. Adivina antes de que se acaben los intentos.</p>
                <p><strong>Tip:</strong> Fíjate en colores, formas y siluetas distintivas.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-red-400 mb-2">🎈 Modo Globos (6 intentos)</h3>
                <p><strong>Objetivo:</strong> Adivina el globo secreto por sus propiedades.</p>
                <p><strong>Cómo jugar:</strong> Similar al modo clásico, pero con globos, MOABs y jefes.</p>
                <p><strong>Tip:</strong> Presta atención al tipo (Básico/Especial/MOAB/Jefe) y habilidades especiales.</p>
            </div>

            <div class="bg-blue-900/30 p-3 rounded-lg">
                <h3 class="text-lg font-bold text-blue-300 mb-2">💡 Consejos Generales</h3>
                <ul class="list-disc list-inside space-y-1">
                    <li>Usa el sistema de búsqueda para encontrar rápidamente torres/héroes</li>
                    <li>Los intentos fallidos se muestran con marco rojo</li>
                    <li>Cada modo se resetea diariamente a las 00:00</li>
                    <li>Puedes cambiar el idioma en cualquier momento</li>
                </ul>
            </div>
        </div>
    ` : `
        <div class="space-y-4">
            <div>
                <h3 class="text-lg font-bold text-blue-400 mb-2">🎯 General Objective</h3>
                <p>Complete all daily game modes. Each mode has a different objective and limited number of attempts.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-green-400 mb-2">🏗️ Classic Mode (6 attempts)</h3>
                <p><strong>Objective:</strong> Guess the secret tower by analyzing its properties.</p>
                <p><strong>How to play:</strong> Type tower or hero names. Each attempt shows which properties match:</p>
                <ul class="list-disc list-inside ml-4 mt-2">
                    <li><span class="bg-green-600 px-2 py-1 rounded text-xs">Green</span> = Correct property</li>
                    <li><span class="bg-yellow-600 px-2 py-1 rounded text-xs">Yellow</span> = Partially correct</li>
                    <li><span class="bg-gray-600 px-2 py-1 rounded text-xs">Gray</span> = Incorrect</li>
                </ul>
                <p class="mt-2"><strong>Tip:</strong> Pay attention to category, cost, and special abilities to narrow down options.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-purple-400 mb-2">💬 Quote Mode (3 attempts)</h3>
                <p><strong>Objective:</strong> Identify which hero said the displayed quote.</p>
                <p><strong>How to play:</strong> Read the quote and type the name of the hero you think said it.</p>
                <p><strong>Tip:</strong> Each hero has characteristic phrases that reflect their personality and abilities.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-yellow-400 mb-2">😄 Emoji Mode (5 attempts)</h3>
                <p><strong>Objective:</strong> Guess the tower or hero represented by emojis.</p>
                <p><strong>How to play:</strong> Analyze the emojis and try to deduce which tower or hero they represent.</p>
                <p><strong>Tip:</strong> Emojis usually relate to the theme, abilities, or appearance of the character.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-cyan-400 mb-2">🖼️ Image Mode (6 attempts)</h3>
                <p><strong>Objective:</strong> Identify the character in the blurred image.</p>
                <p><strong>How to play:</strong> The image gets clearer with each failed attempt. Guess before you run out of tries.</p>
                <p><strong>Tip:</strong> Look for distinctive colors, shapes, and silhouettes.</p>
            </div>

            <div>
                <h3 class="text-lg font-bold text-red-400 mb-2">🎈 Bloons Mode (6 attempts)</h3>
                <p><strong>Objective:</strong> Guess the secret bloon by its properties.</p>
                <p><strong>How to play:</strong> Similar to classic mode, but with bloons, MOABs, and bosses.</p>
                <p><strong>Tip:</strong> Pay attention to type (Basic/Special/MOAB/Boss) and special abilities.</p>
            </div>

            <div class="bg-blue-900/30 p-3 rounded-lg">
                <h3 class="text-lg font-bold text-blue-300 mb-2">💡 General Tips</h3>
                <ul class="list-disc list-inside space-y-1">
                    <li>Use the search system to quickly find towers/heroes</li>
                    <li>Failed attempts are shown with red borders</li>
                    <li>Each mode resets daily at 00:00</li>
                    <li>You can change language at any time</li>
                </ul>
            </div>
        </div>
    `;

    document.getElementById('help-content').innerHTML = helpContent;
}

function hideHelp() {
    document.getElementById('help-modal').classList.add('hidden');
}



// Countdown timer functions
function setupCountdownTimer() {
    updateCountdownTimer();
    // Update every second
    setInterval(updateCountdownTimer, 1000);
}

function updateCountdownTimer() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const timeLeft = tomorrow.getTime() - now.getTime();

    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

    const timerElement = document.getElementById('countdown-timer');
    if (timerElement) {
        timerElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

// Patch notes functions
function showPatchNotes() {
    document.getElementById('patch-notes-modal').classList.remove('hidden');

    const patchNotesContent = currentLanguage === 'es' ? `
        <div class="space-y-4">
            <div class="border-l-4 border-blue-500 pl-4">
                <h3 class="font-bold text-lg text-blue-600">v0.1 - Lanzamiento Inicial</h3>
                <p class="text-sm text-gray-600 mb-2">Fecha: ${new Date().toLocaleDateString('es-ES')}</p>

                <h4 class="font-semibold mt-3 mb-2">🎮 Modos de Juego:</h4>
                <ul class="list-disc list-inside text-sm space-y-1 ml-4">
                    <li><strong>Modo Clásico:</strong> Adivina torres y héroes por sus propiedades</li>
                    <li><strong>Modo Frase:</strong> Identifica héroes por sus frases características</li>
                    <li><strong>Modo Emoji:</strong> Descifra torres y héroes representados por emojis</li>
                    <li><strong>Modo Imagen:</strong> Adivina por imágenes que se van aclarando</li>
                    <li><strong>Modo Globos:</strong> Identifica globos, MOABs y jefes por sus propiedades</li>
                </ul>

                <h4 class="font-semibold mt-3 mb-2">🌍 Características:</h4>
                <ul class="list-disc list-inside text-sm space-y-1 ml-4">
                    <li>Soporte completo para español e inglés</li>
                    <li>Sistema de búsqueda inteligente bilingüe</li>
                    <li>Reset automático diario a las 00:00</li>
                    <li>40+ entidades en el modo emoji</li>
                    <li>Todas las torres, héroes y globos de BTD6</li>
                    <li>Interfaz responsive y moderna</li>
                </ul>

                <h4 class="font-semibold mt-3 mb-2">🎯 Contenido:</h4>
                <ul class="list-disc list-inside text-sm space-y-1 ml-4">
                    <li>24 torres jugables</li>
                    <li>16 héroes con frases traducidas</li>
                    <li>Múltiples tipos de globos y jefes</li>
                    <li>Sistema de feedback visual con colores</li>
                </ul>
            </div>
        </div>
    ` : `
        <div class="space-y-4">
            <div class="border-l-4 border-blue-500 pl-4">
                <h3 class="font-bold text-lg text-blue-600">v0.1 - Initial Release</h3>
                <p class="text-sm text-gray-600 mb-2">Date: ${new Date().toLocaleDateString('en-US')}</p>

                <h4 class="font-semibold mt-3 mb-2">🎮 Game Modes:</h4>
                <ul class="list-disc list-inside text-sm space-y-1 ml-4">
                    <li><strong>Classic Mode:</strong> Guess towers and heroes by their properties</li>
                    <li><strong>Quote Mode:</strong> Identify heroes by their characteristic quotes</li>
                    <li><strong>Emoji Mode:</strong> Decipher towers and heroes represented by emojis</li>
                    <li><strong>Image Mode:</strong> Guess from images that get clearer over time</li>
                    <li><strong>Bloons Mode:</strong> Identify bloons, MOABs and bosses by their properties</li>
                </ul>

                <h4 class="font-semibold mt-3 mb-2">🌍 Features:</h4>
                <ul class="list-disc list-inside text-sm space-y-1 ml-4">
                    <li>Full Spanish and English support</li>
                    <li>Smart bilingual search system</li>
                    <li>Automatic daily reset at 00:00</li>
                    <li>40+ entities in emoji mode</li>
                    <li>All BTD6 towers, heroes and bloons</li>
                    <li>Responsive and modern interface</li>
                </ul>

                <h4 class="font-semibold mt-3 mb-2">🎯 Content:</h4>
                <ul class="list-disc list-inside text-sm space-y-1 ml-4">
                    <li>24 playable towers</li>
                    <li>16 heroes with translated quotes</li>
                    <li>Multiple bloon types and bosses</li>
                    <li>Visual feedback system with colors</li>
                </ul>
            </div>
        </div>
    `;

    document.getElementById('patch-notes-content').innerHTML = patchNotesContent;
}

function hidePatchNotes() {
    document.getElementById('patch-notes-modal').classList.add('hidden');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initApp);
