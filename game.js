// Bloonsdle Multi-Mode Game

// Game data
const towers = [
    { id: 'dart-monkey', name: 'Dart Monkey', category: 'Primary', cost: 200, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: [] },
    { id: 'boomerang-monkey', name: 'Boomerang Monkey', category: 'Primary', cost: 325, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 3, special: ['Comeback'] },
    { id: 'bomb-shooter', name: 'Bomb Shooter', category: 'Primary', cost: 525, damage: 'High', range: 'Medium', attackSpeed: 'Slow', piercing: 999, special: ['Explosive'] },
    { id: 'super-monkey', name: 'Super Monkey', category: 'Magic', cost: 2500, damage: 'High', range: 'Long', attackSpeed: 'Very Fast', piercing: 1, special: ['Plasma'] },
    { id: 'banana-farm', name: 'Banana Farm', category: 'Support', cost: 1150, damage: 'Low', range: 'Short', attackSpeed: 'Slow', piercing: 0, special: ['Income'] },
    { id: 'sniper-monkey', name: '<PERSON>niper Monkey', category: 'Military', cost: 350, damage: 'High', range: 'Very Long', attackSpeed: 'Slow', piercing: 2, special: ['Infinite Range'] }
];

const heroes = [
    { id: 'quincy', name: '<PERSON>', quotes: ['Nothing gets past my bow!', 'I am Quincy, son of <PERSON>!'] },
    { id: 'gwendolin', name: 'Gwendolin', quotes: ['Time to light it up!', 'Burn baby burn!'] },
    { id: 'striker-jones', name: 'Striker Jones', quotes: ['Boom time!', 'Lock and load!'] }
];

const towerEmojis = [
    { towerId: 'dart-monkey', emojis: '🐵🎯', hint: 'Monkey with targeting' },
    { towerId: 'bomb-shooter', emojis: '💣💥', hint: 'Explosive projectiles' },
    { towerId: 'super-monkey', emojis: '🦸‍♂️⚡', hint: 'Superhero with power' }
];

// Current game state
let currentMode = null;
let gameData = {};

// Initialize app
function initApp() {
    showMainMenu();
    updateMenuStatus();
}

function showMainMenu() {
    document.getElementById('main-menu').classList.remove('hidden');
    document.getElementById('game-screen').classList.add('hidden');
}

function startGame(mode) {
    currentMode = mode;
    document.getElementById('main-menu').classList.add('hidden');
    document.getElementById('game-screen').classList.remove('hidden');
    
    document.getElementById('game-title').textContent = `BLOONSDLE - ${getModeName(mode)}`;
    document.getElementById('game-subtitle').textContent = getModeDescription(mode);
    
    initGameData(mode);
    renderGame(mode);
}

function getModeName(mode) {
    const names = {
        classic: 'Clásico',
        quote: 'Frase',
        upgrade: 'Mejora',
        emoji: 'Emoji',
        image: 'Imagen'
    };
    return names[mode] || mode;
}

function getModeDescription(mode) {
    const descriptions = {
        classic: 'Adivina la torre por sus propiedades',
        quote: 'Adivina el héroe por su frase',
        upgrade: 'Adivina la torre por su mejora',
        emoji: 'Adivina la torre por emojis',
        image: 'Adivina la torre por su imagen'
    };
    return descriptions[mode] || '';
}

function initGameData(mode) {
    const today = new Date().toISOString().split('T')[0];
    const saved = localStorage.getItem(`bloonsdle-${mode}`);
    
    if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed.date === today) {
            gameData[mode] = parsed;
            return;
        }
    }
    
    // Create new game
    gameData[mode] = {
        mode: mode,
        date: today,
        guesses: [],
        status: 'playing',
        maxGuesses: 6,
        target: getRandomTarget(mode)
    };
    
    saveGame(mode);
}

function getRandomTarget(mode) {
    const today = new Date().toISOString().split('T')[0];
    let hash = 0;
    for (let i = 0; i < today.length; i++) {
        hash = ((hash << 5) - hash) + today.charCodeAt(i);
    }
    
    switch (mode) {
        case 'classic':
        case 'image':
            return towers[Math.abs(hash) % towers.length];
        case 'quote':
            return heroes[Math.abs(hash + 1) % heroes.length];
        case 'emoji':
            return towerEmojis[Math.abs(hash + 2) % towerEmojis.length];
        default:
            return towers[0];
    }
}

function renderGame(mode) {
    const content = document.getElementById('game-content');
    
    switch (mode) {
        case 'classic':
            renderClassicGame();
            break;
        case 'quote':
            renderQuoteGame();
            break;
        case 'emoji':
            renderEmojiGame();
            break;
        default:
            content.innerHTML = '<div class="text-white text-center">Modo en desarrollo</div>';
    }
}

function renderClassicGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');
    
    content.innerHTML = `
        <div class="w-full max-w-4xl">
            <div class="grid grid-cols-8 gap-1 mb-2 text-xs font-semibold text-white">
                <div class="p-2 text-center">Nombre</div>
                <div class="p-2 text-center">Categoría</div>
                <div class="p-2 text-center">Costo</div>
                <div class="p-2 text-center">Daño</div>
                <div class="p-2 text-center">Rango</div>
                <div class="p-2 text-center">Velocidad</div>
                <div class="p-2 text-center">Penetración</div>
                <div class="p-2 text-center">Especial</div>
            </div>
            <div id="classic-guesses"></div>
        </div>
        
        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="tower-input" placeholder="Escribe el nombre de una torre..." 
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="tower-suggestions" class="mt-2"></div>
            </div>
        ` : `
            <div class="mt-8 text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Ganaste!' : '¡Perdiste!'}</div>
                <div class="mb-4">La torre era: <strong>${game.target.name}</strong></div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;
    
    renderClassicGuesses();
    if (game.status === 'playing') {
        setupTowerInput();
    }
}

function renderQuoteGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');
    
    const randomQuote = game.target.quotes[Math.floor(Math.random() * game.target.quotes.length)];
    
    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="text-3xl mb-4">"${randomQuote}"</div>
            <div class="text-lg">¿Qué héroe dijo esta frase?</div>
        </div>
        
        ${game.status === 'playing' ? `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md">
                ${heroes.map(hero => `
                    <button onclick="makeGuess('${hero.id}')" 
                            class="p-4 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors">
                        ${hero.name}
                    </button>
                `).join('')}
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Correcto!' : '¡Incorrecto!'}</div>
                <div class="mb-4">Era: <strong>${game.target.name}</strong></div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;
}

function renderEmojiGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');
    
    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="text-6xl mb-4">${game.target.emojis}</div>
            <div class="text-lg">Pista: ${game.target.hint}</div>
        </div>
        
        ${game.status === 'playing' ? `
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-lg">
                ${towers.map(tower => `
                    <button onclick="makeGuess('${tower.id}')" 
                            class="p-4 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-sm">
                        ${tower.name}
                    </button>
                `).join('')}
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Correcto!' : '¡Incorrecto!'}</div>
                <div class="mb-4">Era: <strong>${towers.find(t => t.id === game.target.towerId)?.name}</strong></div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;
}

function makeGuess(id) {
    const game = gameData[currentMode];
    if (game.status !== 'playing') return;
    
    let correct = false;
    
    switch (currentMode) {
        case 'quote':
            correct = id === game.target.id;
            break;
        case 'emoji':
            correct = id === game.target.towerId;
            break;
    }
    
    game.guesses.push({ id, correct });
    
    if (correct) {
        game.status = 'won';
    } else if (game.guesses.length >= game.maxGuesses) {
        game.status = 'lost';
    }
    
    saveGame(currentMode);
    renderGame(currentMode);
    updateMenuStatus();
}

function goBackToMenu() {
    showMainMenu();
}

function saveGame(mode) {
    localStorage.setItem(`bloonsdle-${mode}`, JSON.stringify(gameData[mode]));
}

function updateMenuStatus() {
    // Update menu status indicators
    ['classic', 'quote', 'upgrade', 'emoji', 'image'].forEach(mode => {
        const element = document.getElementById(`${mode}-status`);
        if (!element) return;
        
        const saved = localStorage.getItem(`bloonsdle-${mode}`);
        if (saved) {
            const game = JSON.parse(saved);
            const today = new Date().toISOString().split('T')[0];
            
            if (game.date === today) {
                if (game.status === 'won') {
                    element.className = 'game-mode-status completed';
                    element.innerHTML = '<span class="status-text">✓ Completado</span>';
                } else if (game.status === 'lost') {
                    element.className = 'game-mode-status failed';
                    element.innerHTML = '<span class="status-text">✗ Fallido</span>';
                } else {
                    element.className = 'game-mode-status';
                    element.innerHTML = `<span class="status-text">${game.guesses.length}/${game.maxGuesses}</span>`;
                }
                return;
            }
        }
        
        element.className = 'game-mode-status';
        element.innerHTML = '<span class="status-text">Jugar</span>';
    });
}

// Helper functions for classic mode
function renderClassicGuesses() {
    // Implementation for classic mode guesses rendering
}

function setupTowerInput() {
    // Implementation for tower input setup
}

function showGlobalStats() {
    alert('Estadísticas globales - En desarrollo');
}

function showHelp() {
    document.getElementById('help-modal').classList.remove('hidden');
    document.getElementById('help-content').innerHTML = `
        <p><strong>Objetivo:</strong> Completa todos los modos de juego diarios.</p>
        <p><strong>Clásico:</strong> Adivina la torre por sus propiedades.</p>
        <p><strong>Frase:</strong> Adivina el héroe por su frase característica.</p>
        <p><strong>Emoji:</strong> Adivina la torre representada por emojis.</p>
    `;
}

function hideHelp() {
    document.getElementById('help-modal').classList.add('hidden');
}

function hideGlobalStats() {
    document.getElementById('stats-modal').classList.add('hidden');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initApp);
