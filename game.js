// Bloonsdle Game Logic

// Tower database
const towers = [
    // Primary Towers
    { id: 'dart-monkey', name: 'Dart Monkey', category: 'Primary', cost: 200, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: [] },
    { id: 'boomerang-monkey', name: 'Boomerang Monkey', category: 'Primary', cost: 325, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 3, special: ['Comeback'] },
    { id: 'bomb-shooter', name: 'Bomb Shooter', category: 'Primary', cost: 525, damage: 'High', range: 'Medium', attackSpeed: 'Slow', piercing: 999, special: ['Explosive', 'Black Immune'] },
    { id: 'tack-shooter', name: 'Tack Shooter', category: 'Primary', cost: 280, damage: 'Low', range: 'Short', attackSpeed: 'Fast', piercing: 1, special: ['360° Attack'] },
    { id: 'ice-monkey', name: 'Ice Monkey', category: 'Primary', cost: 500, damage: 'Low', range: 'Short', attackSpeed: 'Slow', piercing: 999, special: ['Freeze', 'White Immune'] },
    { id: 'glue-gunner', name: 'Glue Gunner', category: 'Primary', cost: 275, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Slow', 'Purple Immune'] },
    
    // Military Towers
    { id: 'sniper-monkey', name: 'Sniper Monkey', category: 'Military', cost: 350, damage: 'High', range: 'Very Long', attackSpeed: 'Slow', piercing: 2, special: ['Infinite Range', 'Camo Detection'] },
    { id: 'monkey-sub', name: 'Monkey Sub', category: 'Military', cost: 325, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 2, special: ['Water Only', 'Camo Detection'] },
    { id: 'monkey-buccaneer', name: 'Monkey Buccaneer', category: 'Military', cost: 500, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 3, special: ['Water Only', 'Income Generation'] },
    { id: 'monkey-ace', name: 'Monkey Ace', category: 'Military', cost: 800, damage: 'Medium', range: 'Very Long', attackSpeed: 'Medium', piercing: 4, special: ['Flying', 'Map Coverage'] },
    { id: 'heli-pilot', name: 'Heli Pilot', category: 'Military', cost: 1000, damage: 'Medium', range: 'Medium', attackSpeed: 'Fast', piercing: 2, special: ['Flying', 'Moveable', 'Camo Detection'] },
    { id: 'mortar-monkey', name: 'Mortar Monkey', category: 'Military', cost: 700, damage: 'High', range: 'Very Long', attackSpeed: 'Slow', piercing: 999, special: ['Explosive', 'Manual Targeting'] },
    { id: 'dartling-gunner', name: 'Dartling Gunner', category: 'Military', cost: 850, damage: 'Medium', range: 'Long', attackSpeed: 'Very Fast', piercing: 1, special: ['Manual Targeting'] },
    
    // Magic Towers
    { id: 'wizard-monkey', name: 'Wizard Monkey', category: 'Magic', cost: 375, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 2, special: ['Magic', 'Lead Popping'] },
    { id: 'super-monkey', name: 'Super Monkey', category: 'Magic', cost: 2500, damage: 'High', range: 'Long', attackSpeed: 'Very Fast', piercing: 1, special: ['Plasma'] },
    { id: 'ninja-monkey', name: 'Ninja Monkey', category: 'Magic', cost: 500, damage: 'Low', range: 'Medium', attackSpeed: 'Fast', piercing: 2, special: ['Camo Detection', 'Seeking'] },
    { id: 'alchemist', name: 'Alchemist', category: 'Magic', cost: 550, damage: 'Medium', range: 'Short', attackSpeed: 'Medium', piercing: 1, special: ['Lead Popping', 'Buff'] },
    { id: 'druid', name: 'Druid', category: 'Magic', cost: 425, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Nature', 'Thorns'] },
    
    // Support Towers
    { id: 'banana-farm', name: 'Banana Farm', category: 'Support', cost: 1150, damage: 'Low', range: 'Short', attackSpeed: 'Slow', piercing: 0, special: ['Income Generation', 'No Attack'] },
    { id: 'spike-factory', name: 'Spike Factory', category: 'Support', cost: 1000, damage: 'Medium', range: 'Short', attackSpeed: 'Slow', piercing: 5, special: ['Spike Placement', 'End of Track'] },
    { id: 'monkey-village', name: 'Monkey Village', category: 'Support', cost: 1200, damage: 'Low', range: 'Medium', attackSpeed: 'Slow', piercing: 0, special: ['Buff Towers', 'No Direct Attack'] },
    { id: 'engineer-monkey', name: 'Engineer Monkey', category: 'Support', cost: 400, damage: 'Low', range: 'Short', attackSpeed: 'Medium', piercing: 2, special: ['Sentries', 'Lead Popping'] }
];

// Game state
let gameState = {
    targetTower: null,
    guesses: [],
    gameStatus: 'playing', // 'playing', 'won', 'lost'
    maxGuesses: 6
};

// Game stats
let gameStats = {
    gamesPlayed: 0,
    gamesWon: 0,
    currentStreak: 0,
    maxStreak: 0,
    guessDistribution: {},
    lastPlayedDate: ''
};

// Initialize game
function initGame() {
    loadGameStats();
    
    const today = new Date().toISOString().split('T')[0];
    const savedGame = localStorage.getItem('bloonsdle-game-state');
    
    if (savedGame) {
        const parsed = JSON.parse(savedGame);
        if (parsed.date === today) {
            gameState = parsed;
            renderGame();
            return;
        }
    }
    
    // Start new game
    gameState.targetTower = getTodaysTower();
    gameState.guesses = [];
    gameState.gameStatus = 'playing';
    
    saveGameState();
    renderGame();
}

// Get today's tower (deterministic based on date)
function getTodaysTower() {
    const today = new Date().toISOString().split('T')[0];
    let hash = 0;
    for (let i = 0; i < today.length; i++) {
        const char = today.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    const index = Math.abs(hash) % towers.length;
    return towers[index];
}

// Generate feedback for a guess
function generateFeedback(guess, target) {
    return {
        name: guess.name === target.name ? 'correct' : 'absent',
        category: guess.category === target.category ? 'correct' : 'absent',
        cost: guess.cost === target.cost ? 'correct' : 
              guess.cost > target.cost ? 'lower' : 'higher',
        damage: guess.damage === target.damage ? 'correct' : 'absent',
        range: guess.range === target.range ? 'correct' : 'absent',
        attackSpeed: guess.attackSpeed === target.attackSpeed ? 'correct' : 'absent',
        piercing: guess.piercing === target.piercing ? 'correct' :
                  guess.piercing > target.piercing ? 'lower' : 'higher',
        special: compareSpecialAbilities(guess.special, target.special)
    };
}

function compareSpecialAbilities(guessSpecial, targetSpecial) {
    if (arraysEqual(guessSpecial, targetSpecial)) {
        return 'correct';
    }
    const hasCommonAbility = guessSpecial.some(ability => targetSpecial.includes(ability));
    return hasCommonAbility ? 'partial' : 'absent';
}

function arraysEqual(a, b) {
    if (a.length !== b.length) return false;
    const sortedA = [...a].sort();
    const sortedB = [...b].sort();
    return sortedA.every((val, index) => val === sortedB[index]);
}

// Make a guess
function makeGuess(tower) {
    if (gameState.gameStatus !== 'playing' || gameState.guesses.length >= gameState.maxGuesses) {
        return;
    }
    
    const feedback = generateFeedback(tower, gameState.targetTower);
    const guess = { tower, feedback };
    
    gameState.guesses.push(guess);
    
    // Check win condition
    if (feedback.name === 'correct') {
        gameState.gameStatus = 'won';
        updateGameStats(true, gameState.guesses.length);
    } else if (gameState.guesses.length >= gameState.maxGuesses) {
        gameState.gameStatus = 'lost';
        updateGameStats(false, gameState.guesses.length);
    }
    
    saveGameState();
    renderGame();
}

// Save/Load functions
function saveGameState() {
    const dataToSave = {
        ...gameState,
        date: new Date().toISOString().split('T')[0]
    };
    localStorage.setItem('bloonsdle-game-state', JSON.stringify(dataToSave));
}

function loadGameStats() {
    const saved = localStorage.getItem('bloonsdle-game-stats');
    if (saved) {
        gameStats = JSON.parse(saved);
    }
}

function saveGameStats() {
    localStorage.setItem('bloonsdle-game-stats', JSON.stringify(gameStats));
}

function updateGameStats(won, guessCount) {
    const today = new Date().toISOString().split('T')[0];
    
    gameStats.gamesPlayed++;
    if (won) {
        gameStats.gamesWon++;
        gameStats.currentStreak++;
        gameStats.maxStreak = Math.max(gameStats.maxStreak, gameStats.currentStreak);
        gameStats.guessDistribution[guessCount] = (gameStats.guessDistribution[guessCount] || 0) + 1;
    } else {
        gameStats.currentStreak = 0;
    }
    
    gameStats.lastPlayedDate = today;
    saveGameStats();
}

// Render functions
function renderGame() {
    renderGuesses();
    renderEmptyRows();
    renderSearchInput();
    renderGameEndMessage();
    renderStats();
}

function renderGuesses() {
    const container = document.getElementById('guesses-grid');
    container.innerHTML = '';
    
    gameState.guesses.forEach(guess => {
        const row = createGuessRow(guess);
        container.appendChild(row);
    });
}

function createGuessRow(guess) {
    const row = document.createElement('div');
    row.className = 'grid grid-cols-9 gap-1 mb-1';
    
    const cells = [
        createImageCell(guess.tower, guess.feedback.name),
        createTextCell(guess.tower.name, guess.feedback.name),
        createTextCell(guess.tower.category, guess.feedback.category),
        createCostCell(guess.tower.cost, guess.feedback.cost),
        createTextCell(guess.tower.damage, guess.feedback.damage),
        createTextCell(guess.tower.range, guess.feedback.range),
        createTextCell(guess.tower.attackSpeed, guess.feedback.attackSpeed),
        createPiercingCell(guess.tower.piercing, guess.feedback.piercing),
        createSpecialCell(guess.tower.special, guess.feedback.special)
    ];
    
    cells.forEach(cell => row.appendChild(cell));
    return row;
}

function createImageCell(tower, feedback) {
    const cell = document.createElement('div');
    cell.className = `p-2 flex items-center justify-center rounded game-tile ${feedback}`;
    
    const img = document.createElement('div');
    img.className = 'tower-image';
    img.textContent = tower.name.split(' ').map(word => word[0]).join('');
    
    cell.appendChild(img);
    return cell;
}

function createTextCell(text, feedback) {
    const cell = document.createElement('div');
    cell.className = `p-2 text-center text-xs font-medium rounded game-tile ${feedback}`;
    cell.textContent = text;
    return cell;
}

function createCostCell(cost, feedback) {
    const cell = document.createElement('div');
    cell.className = `p-2 text-center text-xs font-medium rounded game-tile ${feedback}`;
    let text = `$${cost}`;
    if (feedback === 'higher') text += ' ↑';
    if (feedback === 'lower') text += ' ↓';
    cell.textContent = text;
    return cell;
}

function createPiercingCell(piercing, feedback) {
    const cell = document.createElement('div');
    cell.className = `p-2 text-center text-xs font-medium rounded game-tile ${feedback}`;
    let text = piercing === 999 ? '∞' : piercing.toString();
    if (feedback === 'higher') text += ' ↑';
    if (feedback === 'lower') text += ' ↓';
    cell.textContent = text;
    return cell;
}

function createSpecialCell(special, feedback) {
    const cell = document.createElement('div');
    cell.className = `p-2 text-center text-xs font-medium rounded game-tile ${feedback}`;
    cell.textContent = special.join(', ') || 'None';
    return cell;
}

function renderEmptyRows() {
    const container = document.getElementById('empty-rows');
    container.innerHTML = '';
    
    const emptyRows = gameState.maxGuesses - gameState.guesses.length;
    
    for (let i = 0; i < emptyRows; i++) {
        const row = document.createElement('div');
        row.className = 'grid grid-cols-9 gap-1 mb-1';
        
        for (let j = 0; j < 9; j++) {
            const cell = document.createElement('div');
            cell.className = 'p-2 text-center text-xs font-medium rounded border-2 border-gray-300 bg-white/10';
            row.appendChild(cell);
        }
        
        container.appendChild(row);
    }
}

function renderSearchInput() {
    const container = document.getElementById('search-container');
    const input = document.getElementById('tower-search');
    
    if (gameState.gameStatus !== 'playing') {
        container.style.display = 'none';
    } else {
        container.style.display = 'block';
        setupSearchInput();
    }
}

function setupSearchInput() {
    const input = document.getElementById('tower-search');
    const results = document.getElementById('search-results');
    
    input.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();
        
        if (query.length === 0) {
            results.classList.add('hidden');
            return;
        }
        
        const filtered = towers.filter(tower => 
            tower.name.toLowerCase().includes(query)
        ).slice(0, 8);
        
        if (filtered.length === 0) {
            results.classList.add('hidden');
            return;
        }
        
        results.innerHTML = '';
        filtered.forEach(tower => {
            const item = document.createElement('div');
            item.className = 'px-4 py-3 cursor-pointer hover:bg-gray-100 flex items-center gap-3';
            
            const img = document.createElement('div');
            img.className = 'tower-image';
            img.style.width = '40px';
            img.style.height = '40px';
            img.textContent = tower.name.split(' ').map(word => word[0]).join('');
            
            const info = document.createElement('div');
            info.className = 'flex-1';
            info.innerHTML = `
                <div class="font-medium">${tower.name}</div>
                <div class="text-sm text-gray-500">${tower.category} • $${tower.cost}</div>
            `;
            
            item.appendChild(img);
            item.appendChild(info);
            
            item.addEventListener('click', () => {
                makeGuess(tower);
                input.value = '';
                results.classList.add('hidden');
            });
            
            results.appendChild(item);
        });
        
        results.classList.remove('hidden');
    });
    
    // Hide results when clicking outside
    document.addEventListener('click', (e) => {
        if (!input.contains(e.target) && !results.contains(e.target)) {
            results.classList.add('hidden');
        }
    });
}

function renderGameEndMessage() {
    const container = document.getElementById('game-end-message');
    
    if (gameState.gameStatus === 'playing') {
        container.classList.add('hidden');
        return;
    }
    
    container.classList.remove('hidden');
    
    const won = gameState.gameStatus === 'won';
    container.innerHTML = `
        <div class="${won ? 'text-green-400' : 'text-red-400'} text-2xl font-bold mb-4">
            ${won ? '¡Felicitaciones!' : '¡Juego Terminado!'}
        </div>
        <div class="text-white mb-4">
            La torre era: <span class="font-bold">${gameState.targetTower.name}</span>
        </div>
        ${won ? `<div class="text-white mb-4">¡Lo adivinaste en ${gameState.guesses.length} intentos!</div>` : ''}
        <div class="text-white/80 text-sm">
            ¡Vuelve mañana para una nueva torre!
        </div>
    `;
}

function renderStats() {
    const container = document.getElementById('stats-display');
    
    if (gameState.gameStatus === 'playing') {
        container.classList.add('hidden');
        return;
    }
    
    container.classList.remove('hidden');
    
    const winRate = gameStats.gamesPlayed > 0 ? Math.round((gameStats.gamesWon / gameStats.gamesPlayed) * 100) : 0;
    
    container.innerHTML = `
        <h3 class="text-lg font-bold mb-4">Tus Estadísticas</h3>
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <div class="text-2xl font-bold">${gameStats.gamesPlayed}</div>
                <div>Jugados</div>
            </div>
            <div>
                <div class="text-2xl font-bold">${winRate}%</div>
                <div>% Victorias</div>
            </div>
            <div>
                <div class="text-2xl font-bold">${gameStats.currentStreak}</div>
                <div>Racha Actual</div>
            </div>
            <div>
                <div class="text-2xl font-bold">${gameStats.maxStreak}</div>
                <div>Mejor Racha</div>
            </div>
        </div>
    `;
}

// Modal functions
function showHelp() {
    document.getElementById('help-modal').classList.remove('hidden');
}

function hideHelp() {
    document.getElementById('help-modal').classList.add('hidden');
}

function showStats() {
    renderStats();
    document.getElementById('stats-display').classList.remove('hidden');
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', initGame);
