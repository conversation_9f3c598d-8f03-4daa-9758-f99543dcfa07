// Bloonsdle Multi-Mode Game

// Game data
const towers = [
    { id: 'dartMonkey', name: 'Dart Monkey', category: 'Primary', cost: 200, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: [] },
    { id: 'boomerangMonkey', name: 'Boomerang Monkey', category: 'Primary', cost: 325, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 3, special: ['Comeback'] },
    { id: 'bombShooter', name: 'Bomb Shooter', category: 'Primary', cost: 525, damage: 'High', range: 'Medium', attackSpeed: 'Slow', piercing: 999, special: ['Explosive'] },
    { id: 'tackShooter', name: 'Tack Shooter', category: 'Primary', cost: 280, damage: 'Low', range: 'Short', attackSpeed: 'Fast', piercing: 1, special: ['360° Attack'] },
    { id: 'iceMonkey', name: 'Ice Monkey', category: 'Primary', cost: 500, damage: 'Low', range: 'Short', attackSpeed: 'Slow', piercing: 999, special: ['Freeze'] },
    { id: 'glueGunner', name: 'Glue Gunner', category: 'Primary', cost: 275, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Slow'] },
    { id: 'sniperMonkey', name: 'Sniper Monkey', category: 'Military', cost: 350, damage: 'High', range: 'Very Long', attackSpeed: 'Slow', piercing: 2, special: ['Infinite Range'] },
    { id: 'monkeySub', name: 'Monkey Sub', category: 'Military', cost: 325, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 2, special: ['Water Only'] },
    { id: 'monkeyBuccaneer', name: 'Monkey Buccaneer', category: 'Military', cost: 500, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 3, special: ['Water Only'] },
    { id: 'monkeyAce', name: 'Monkey Ace', category: 'Military', cost: 800, damage: 'Medium', range: 'Very Long', attackSpeed: 'Medium', piercing: 4, special: ['Flying'] },
    { id: 'heliPilot', name: 'Heli Pilot', category: 'Military', cost: 1000, damage: 'Medium', range: 'Medium', attackSpeed: 'Fast', piercing: 2, special: ['Flying'] },
    { id: 'mortarMonkey', name: 'Mortar Monkey', category: 'Military', cost: 700, damage: 'High', range: 'Very Long', attackSpeed: 'Slow', piercing: 999, special: ['Explosive'] },
    { id: 'dartlingGunner', name: 'Dartling Gunner', category: 'Military', cost: 850, damage: 'Medium', range: 'Long', attackSpeed: 'Very Fast', piercing: 1, special: ['Manual Targeting'] },
    { id: 'monkeyApprentice', name: 'Wizard Monkey', category: 'Magic', cost: 375, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 2, special: ['Magic'] },
    { id: 'superMonkey', name: 'Super Monkey', category: 'Magic', cost: 2500, damage: 'High', range: 'Long', attackSpeed: 'Very Fast', piercing: 1, special: ['Plasma'] },
    { id: 'ninjaMonkey', name: 'Ninja Monkey', category: 'Magic', cost: 500, damage: 'Low', range: 'Medium', attackSpeed: 'Fast', piercing: 2, special: ['Camo Detection'] },
    { id: 'monkeyAlchemist', name: 'Alchemist', category: 'Magic', cost: 550, damage: 'Medium', range: 'Short', attackSpeed: 'Medium', piercing: 1, special: ['Lead Popping'] },
    { id: 'druidMonkey', name: 'Druid', category: 'Magic', cost: 425, damage: 'Low', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Nature'] },
    { id: 'bananaFarm', name: 'Banana Farm', category: 'Support', cost: 1150, damage: 'Low', range: 'Short', attackSpeed: 'Slow', piercing: 0, special: ['Income'] },
    { id: 'spikeFactory', name: 'Spike Factory', category: 'Support', cost: 1000, damage: 'Medium', range: 'Short', attackSpeed: 'Slow', piercing: 5, special: ['Spike Placement'] },
    { id: 'monkeyVillage', name: 'Monkey Village', category: 'Support', cost: 1200, damage: 'Low', range: 'Medium', attackSpeed: 'Slow', piercing: 0, special: ['Buff Towers'] },
    { id: 'engineerMonkey', name: 'Engineer Monkey', category: 'Support', cost: 400, damage: 'Low', range: 'Short', attackSpeed: 'Medium', piercing: 2, special: ['Sentries'] },
    { id: 'merMonkey', name: 'Beast Handler', category: 'Support', cost: 800, damage: 'Medium', range: 'Medium', attackSpeed: 'Medium', piercing: 1, special: ['Beast Control'] }
];

const heroes = [
    { id: 'quincy', name: 'Quincy', quotes: ['Nothing gets past my bow!', 'I am Quincy, son of Quincy!', 'My bow is ready!'] },
    { id: 'gwendolin', name: 'Gwendolin', quotes: ['Time to light it up!', 'Burn baby burn!', 'Feel the heat!'] },
    { id: 'strikerJones', name: 'Striker Jones', quotes: ['Boom time!', 'Lock and load!', 'Artillery incoming!'] },
    { id: 'obynGreenFoot', name: 'Obyn Greenfoot', quotes: ['Nature will protect us!', 'The forest spirits guide me!', 'One with nature!'] },
    { id: 'captainChurchill', name: 'Captain Churchill', quotes: ['Tank beats everything!', 'Armor up!', 'Heavy artillery ready!'] },
    { id: 'benjamin', name: 'Benjamin', quotes: ['Hacking the mainframe!', 'Money talks!', 'System compromised!'] },
    { id: 'ezili', name: 'Ezili', quotes: ['Dark magic flows through me!', 'Cursed be your balloons!', 'Voodoo power!'] },
    { id: 'patFusty', name: 'Pat Fusty', quotes: ['Big hugs for everyone!', 'Pat smash!', 'Friendly giant coming through!'] },
    { id: 'adora', name: 'Adora', quotes: ['The sun god demands sacrifice!', 'Light will purge the darkness!', 'Divine power!'] },
    { id: 'admiralBrickell', name: 'Admiral Brickell', quotes: ['Naval superiority!', 'All hands on deck!', 'Anchors aweigh!'] },
    { id: 'etienne', name: 'Etienne', quotes: ['Drone squadron ready!', 'Technology is the future!', 'Surveillance online!'] },
    { id: 'sauda', name: 'Sauda', quotes: ['The blade cuts true!', 'Honor guides my sword!', 'Swift and deadly!'] },
    { id: 'psi', name: 'Psi', quotes: ['Mind over matter!', 'Psychic energy flows!', 'The power of thought!'] },
    { id: 'geraldo', name: 'Geraldo', quotes: ['I have just the thing!', 'My shop has everything!', 'Quality goods for sale!'] },
    { id: 'corvus', name: 'Corvus', quotes: ['Dark magic awakens!', 'The shadows obey me!', 'Power beyond measure!'] },
    { id: 'rosalia', name: 'Rosalia', quotes: ['Let the music play!', 'Dance to victory!', 'Rhythm and power!'] },
    { id: 'beastHandler', name: 'Beast Handler', quotes: ['My creatures are ready!', 'Nature fights with us!', 'Wild allies unite!'] }
];

const bloons = [
    { id: 'redBloon', name: 'Red Bloon', type: 'Basic', health: 1, speed: 'Medium', special: [] },
    { id: 'blueBloon', name: 'Blue Bloon', type: 'Basic', health: 2, speed: 'Fast', special: [] },
    { id: 'greenBloon', name: 'Green Bloon', type: 'Basic', health: 3, speed: 'Medium', special: [] },
    { id: 'yellowBloon', name: 'Yellow Bloon', type: 'Basic', health: 4, speed: 'Very Fast', special: [] },
    { id: 'pinkBloon', name: 'Pink Bloon', type: 'Basic', health: 5, speed: 'Very Fast', special: [] },
    { id: 'blackBloon', name: 'Black Bloon', type: 'Special', health: 11, speed: 'Medium', special: ['Explosion Immune'] },
    { id: 'whiteBloon', name: 'White Bloon', type: 'Special', health: 11, speed: 'Medium', special: ['Freeze Immune'] },
    { id: 'leadBloon', name: 'Lead Bloon', type: 'Special', health: 23, speed: 'Slow', special: ['Sharp Immune'] },
    { id: 'zebraBloon', name: 'Zebra Bloon', type: 'Special', health: 23, speed: 'Medium', special: ['Explosion Immune', 'Freeze Immune'] },
    { id: 'rainbowBloon', name: 'Rainbow Bloon', type: 'Special', health: 47, speed: 'Fast', special: [] },
    { id: 'ceramicBloon', name: 'Ceramic Bloon', type: 'Special', health: 104, speed: 'Fast', special: ['Extra Tough'] },
    { id: 'purpleBloon', name: 'Purple Bloon', type: 'Special', health: 11, speed: 'Medium', special: ['Magic Immune'] }
];

const moabs = [
    { id: 'redMOAB', name: 'MOAB', type: 'MOAB', health: 616, speed: 'Slow', special: ['Massive'] },
    { id: 'blueMOAB', name: 'BFB', type: 'MOAB', health: 700, speed: 'Slow', special: ['Big', 'Fortified'] },
    { id: 'greenMOAB', name: 'ZOMG', type: 'MOAB', health: 4000, speed: 'Slow', special: ['Zeppelin', 'Massive'] },
    { id: 'purpleMOAB', name: 'DDT', type: 'MOAB', health: 400, speed: 'Fast', special: ['Camo', 'Lead', 'Black Properties'] },
    { id: 'blackMOAB', name: 'BAD', type: 'MOAB', health: 20000, speed: 'Slow', special: ['Boss', 'Immune to Most Abilities'] }
];

const bosses = [
    { id: 'bloonariusBoss', name: 'Bloonarius', type: 'Boss', health: 'Variable', speed: 'Slow', special: ['Spawns Bloons', 'Regenerates'] },
    { id: 'lychBoss', name: 'Lych', type: 'Boss', health: 'Variable', speed: 'Medium', special: ['Steals Buffs', 'Soul Barrier'] },
    { id: 'vortexBoss', name: 'Vortex', type: 'Boss', health: 'Variable', speed: 'Fast', special: ['Stuns Towers', 'Teleports'] },
    { id: 'dreadbloonBoss', name: 'Dreadbloon', type: 'Boss', health: 'Variable', speed: 'Medium', special: ['Rock Armor', 'Spawns Rocks'] },
    { id: 'phayzeBoss', name: 'Phayze', type: 'Boss', health: 'Variable', speed: 'Variable', special: ['Phase Shifts', 'Teleports'] }
];

const towerEmojis = [
    { towerId: 'dartMonkey', emojis: '🐵🎯', hint: 'Monkey with targeting' },
    { towerId: 'bombShooter', emojis: '💣💥', hint: 'Explosive projectiles' },
    { towerId: 'superMonkey', emojis: '🦸‍♂️⚡', hint: 'Superhero with power' },
    { towerId: 'bananaFarm', emojis: '🍌🚜', hint: 'Fruit farming' },
    { towerId: 'ninjaMonkey', emojis: '🥷⭐', hint: 'Stealthy warrior' }
];

const heroEmojis = [
    { heroId: 'quincy', emojis: '🏹🎯', hint: 'Archer with perfect aim' },
    { heroId: 'gwendolin', emojis: '🔥💥', hint: 'Fire specialist' },
    { heroId: 'patFusty', emojis: '🦍💪', hint: 'Big friendly giant' }
];

// Current game state
let currentMode = null;
let gameData = {};

// Initialize app
function initApp() {
    showMainMenu();
    updateMenuStatus();
}

function showMainMenu() {
    document.getElementById('main-menu').classList.remove('hidden');
    document.getElementById('game-screen').classList.add('hidden');
}

function startGame(mode) {
    currentMode = mode;
    document.getElementById('main-menu').classList.add('hidden');
    document.getElementById('game-screen').classList.remove('hidden');
    
    document.getElementById('game-title').textContent = `BLOONSDLE - ${getModeName(mode)}`;
    document.getElementById('game-subtitle').textContent = getModeDescription(mode);
    
    initGameData(mode);
    renderGame(mode);
}

function getModeName(mode) {
    const names = {
        classic: 'Clásico',
        quote: 'Frase',
        upgrade: 'Mejora',
        emoji: 'Emoji',
        image: 'Imagen'
    };
    return names[mode] || mode;
}

function getModeDescription(mode) {
    const descriptions = {
        classic: 'Adivina la torre por sus propiedades',
        quote: 'Adivina el héroe por su frase',
        upgrade: 'Adivina la torre por su mejora',
        emoji: 'Adivina por emojis',
        image: 'Adivina por la imagen',
        bloons: 'Adivina el globo por sus propiedades'
    };
    return descriptions[mode] || '';
}

function initGameData(mode) {
    const today = new Date().toISOString().split('T')[0];
    const saved = localStorage.getItem(`bloonsdle-${mode}`);
    
    if (saved) {
        const parsed = JSON.parse(saved);
        if (parsed.date === today) {
            gameData[mode] = parsed;
            return;
        }
    }
    
    // Create new game
    gameData[mode] = {
        mode: mode,
        date: today,
        guesses: [],
        status: 'playing',
        maxGuesses: 6,
        target: getRandomTarget(mode)
    };
    
    saveGame(mode);
}

function getRandomTarget(mode) {
    const today = new Date().toISOString().split('T')[0];
    let hash = 0;
    for (let i = 0; i < today.length; i++) {
        hash = ((hash << 5) - hash) + today.charCodeAt(i);
    }

    switch (mode) {
        case 'classic':
            return towers[Math.abs(hash) % towers.length];
        case 'quote':
            return heroes[Math.abs(hash + 1) % heroes.length];
        case 'emoji':
            // Combine towers and heroes for emoji mode
            const allEmojiTargets = [...towerEmojis, ...heroEmojis];
            return allEmojiTargets[Math.abs(hash + 2) % allEmojiTargets.length];
        case 'image':
            // Combine all entities for image mode
            const allEntities = [...towers, ...heroes, ...bloons, ...moabs, ...bosses];
            return allEntities[Math.abs(hash + 3) % allEntities.length];
        case 'bloons':
            // Combine bloons, moabs, and bosses
            const allBloons = [...bloons, ...moabs, ...bosses];
            return allBloons[Math.abs(hash + 4) % allBloons.length];
        default:
            return towers[0];
    }
}

function renderGame(mode) {
    const content = document.getElementById('game-content');

    switch (mode) {
        case 'classic':
            renderClassicGame();
            break;
        case 'quote':
            renderQuoteGame();
            break;
        case 'emoji':
            renderEmojiGame();
            break;
        case 'image':
            renderImageGame();
            break;
        case 'bloons':
            renderBloonsGame();
            break;
        case 'upgrade':
            content.innerHTML = '<div class="text-white text-center text-xl">🔧 Modo Mejora<br><br>En desarrollo...<br><br><button onclick="goBackToMenu()" class="mt-4 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">Volver al Menú</button></div>';
            break;
        default:
            content.innerHTML = '<div class="text-white text-center">Modo en desarrollo</div>';
    }
}

function renderClassicGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    content.innerHTML = `
        <div class="w-full max-w-5xl">
            <div class="grid grid-cols-9 gap-1 mb-2 text-xs font-semibold text-white">
                <div class="p-2 text-center">Imagen</div>
                <div class="p-2 text-center">Nombre</div>
                <div class="p-2 text-center">Categoría</div>
                <div class="p-2 text-center">Costo</div>
                <div class="p-2 text-center">Daño</div>
                <div class="p-2 text-center">Rango</div>
                <div class="p-2 text-center">Velocidad</div>
                <div class="p-2 text-center">Penetración</div>
                <div class="p-2 text-center">Especial</div>
            </div>
            <div id="classic-guesses"></div>
        </div>

        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="tower-input" placeholder="Escribe el nombre de una torre o héroe..."
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="tower-suggestions" class="mt-2"></div>
            </div>
        ` : `
            <div class="mt-8 text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Ganaste!' : '¡Perdiste!'}</div>
                <div class="mb-4 flex items-center justify-center gap-4">
                    <img src="${getImagePath(game.target)}" alt="${game.target.name}" class="w-16 h-16 rounded-lg">
                    <span>Era: <strong>${game.target.name}</strong></span>
                </div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;

    renderClassicGuesses();
    if (game.status === 'playing') {
        setupTowerInput();
    }
}

function renderQuoteGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    const randomQuote = game.target.quotes[Math.floor(Math.random() * game.target.quotes.length)];

    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="text-3xl mb-4">"${randomQuote}"</div>
            <div class="text-lg">¿Qué héroe dijo esta frase?</div>
        </div>

        ${game.status === 'playing' ? `
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-w-4xl">
                ${heroes.map(hero => `
                    <button onclick="makeGuess('${hero.id}')"
                            class="p-4 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors flex flex-col items-center">
                        <img src="images/heroes/${hero.id}.png" alt="${hero.name}" class="w-12 h-12 mb-2 rounded-lg">
                        <span class="text-sm">${hero.name}</span>
                    </button>
                `).join('')}
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Correcto!' : '¡Incorrecto!'}</div>
                <div class="mb-4 flex items-center justify-center gap-4">
                    <img src="images/heroes/${game.target.id}.png" alt="${game.target.name}" class="w-16 h-16 rounded-lg">
                    <span>Era: <strong>${game.target.name}</strong></span>
                </div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;
}

function renderEmojiGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="text-6xl mb-4">${game.target.emojis}</div>
            <div class="text-lg">Pista: ${game.target.hint}</div>
        </div>

        ${game.status === 'playing' ? `
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl">
                ${towers.map(tower => `
                    <button onclick="makeGuess('${tower.id}')"
                            class="p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-sm">
                        <img src="images/towers/${tower.id}.png" alt="${tower.name}" class="w-8 h-8 mx-auto mb-1">
                        ${tower.name}
                    </button>
                `).join('')}
                ${heroes.map(hero => `
                    <button onclick="makeGuess('${hero.id}')"
                            class="p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-sm">
                        <img src="images/heroes/${hero.id}.png" alt="${hero.name}" class="w-8 h-8 mx-auto mb-1">
                        ${hero.name}
                    </button>
                `).join('')}
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Correcto!' : '¡Incorrecto!'}</div>
                <div class="mb-4">Era: <strong>${getTargetName(game.target)}</strong></div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;
}

function renderImageGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    const imagePath = getImagePath(game.target);

    content.innerHTML = `
        <div class="text-center text-white mb-8">
            <div class="mb-4">
                <img src="${imagePath}" alt="¿Quién es?" class="w-32 h-32 mx-auto rounded-lg border-4 border-white/20">
            </div>
            <div class="text-lg">¿Quién o qué es?</div>
        </div>

        ${game.status === 'playing' ? `
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-4xl">
                ${towers.map(tower => `
                    <button onclick="makeGuess('${tower.id}')"
                            class="p-2 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-xs">
                        <img src="images/towers/${tower.id}.png" alt="${tower.name}" class="w-6 h-6 mx-auto mb-1">
                        ${tower.name}
                    </button>
                `).join('')}
                ${heroes.map(hero => `
                    <button onclick="makeGuess('${hero.id}')"
                            class="p-2 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-xs">
                        <img src="images/heroes/${hero.id}.png" alt="${hero.name}" class="w-6 h-6 mx-auto mb-1">
                        ${hero.name}
                    </button>
                `).join('')}
                ${bloons.map(bloon => `
                    <button onclick="makeGuess('${bloon.id}')"
                            class="p-2 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-xs">
                        <img src="images/bloons/${bloon.id}.png" alt="${bloon.name}" class="w-6 h-6 mx-auto mb-1">
                        ${bloon.name}
                    </button>
                `).join('')}
                ${moabs.map(moab => `
                    <button onclick="makeGuess('${moab.id}')"
                            class="p-2 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-xs">
                        <img src="images/moabs/${moab.id}.png" alt="${moab.name}" class="w-6 h-6 mx-auto mb-1">
                        ${moab.name}
                    </button>
                `).join('')}
                ${bosses.map(boss => `
                    <button onclick="makeGuess('${boss.id}')"
                            class="p-2 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-xs">
                        <img src="images/bosses/${boss.id}.png" alt="${boss.name}" class="w-6 h-6 mx-auto mb-1">
                        ${boss.name}
                    </button>
                `).join('')}
            </div>
        ` : `
            <div class="text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Correcto!' : '¡Incorrecto!'}</div>
                <div class="mb-4">Era: <strong>${game.target.name}</strong></div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;
}

function renderBloonsGame() {
    const game = gameData[currentMode];
    const content = document.getElementById('game-content');

    content.innerHTML = `
        <div class="w-full max-w-4xl">
            <div class="grid grid-cols-6 gap-1 mb-2 text-xs font-semibold text-white">
                <div class="p-2 text-center">Imagen</div>
                <div class="p-2 text-center">Nombre</div>
                <div class="p-2 text-center">Tipo</div>
                <div class="p-2 text-center">Vida</div>
                <div class="p-2 text-center">Velocidad</div>
                <div class="p-2 text-center">Especial</div>
            </div>
            <div id="bloons-guesses"></div>
        </div>

        ${game.status === 'playing' ? `
            <div class="mt-8 w-full max-w-md">
                <input type="text" id="bloon-input" placeholder="Escribe el nombre de un globo..."
                       class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                <div id="bloon-suggestions" class="mt-2"></div>
            </div>
        ` : `
            <div class="mt-8 text-center text-white">
                <div class="text-2xl font-bold mb-4">${game.status === 'won' ? '¡Ganaste!' : '¡Perdiste!'}</div>
                <div class="mb-4">El globo era: <strong>${game.target.name}</strong></div>
                <button onclick="goBackToMenu()" class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Volver al Menú
                </button>
            </div>
        `}
    `;

    renderBloonsGuesses();
    if (game.status === 'playing') {
        setupBloonInput();
    }
}

function getImagePath(target) {
    if (towers.find(t => t.id === target.id)) return `images/towers/${target.id}.png`;
    if (heroes.find(h => h.id === target.id)) return `images/heroes/${target.id}.png`;
    if (bloons.find(b => b.id === target.id)) return `images/bloons/${target.id}.png`;
    if (moabs.find(m => m.id === target.id)) return `images/moabs/${target.id}.png`;
    if (bosses.find(b => b.id === target.id)) return `images/bosses/${target.id}.png`;
    return 'images/towers/dartMonkey.png'; // fallback
}

function getTargetName(target) {
    if (target.towerId) {
        const tower = towers.find(t => t.id === target.towerId);
        return tower ? tower.name : 'Unknown';
    }
    if (target.heroId) {
        const hero = heroes.find(h => h.id === target.heroId);
        return hero ? hero.name : 'Unknown';
    }
    return target.name || 'Unknown';
}

function makeGuess(id) {
    const game = gameData[currentMode];
    if (game.status !== 'playing') return;

    let correct = false;

    switch (currentMode) {
        case 'quote':
            correct = id === game.target.id;
            break;
        case 'emoji':
            correct = id === game.target.towerId || id === game.target.heroId;
            break;
        case 'image':
        case 'bloons':
            correct = id === game.target.id;
            break;
    }

    game.guesses.push({ id, correct });

    if (correct) {
        game.status = 'won';
    } else if (game.guesses.length >= game.maxGuesses) {
        game.status = 'lost';
    }

    saveGame(currentMode);
    renderGame(currentMode);
    updateMenuStatus();
}

function renderBloonsGuesses() {
    const game = gameData[currentMode];
    const container = document.getElementById('bloons-guesses');
    if (!container) return;

    container.innerHTML = '';

    game.guesses.forEach(guess => {
        const guessEntity = findEntityById(guess.id);
        if (!guessEntity) return;

        const row = document.createElement('div');
        row.className = 'grid grid-cols-6 gap-1 mb-1 guess-row';

        const feedback = generateBloonsFeedback(guessEntity, game.target);

        row.innerHTML = `
            <div class="p-2 flex items-center justify-center rounded game-tile ${feedback.image}">
                <img src="${getImagePath(guessEntity)}" alt="${guessEntity.name}" class="w-8 h-8">
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.name}">
                ${guessEntity.name}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.type}">
                ${guessEntity.type}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.health}">
                ${guessEntity.health}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.speed}">
                ${guessEntity.speed}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.special}">
                ${guessEntity.special.join(', ') || 'None'}
            </div>
        `;

        container.appendChild(row);
    });
}

function generateBloonsFeedback(guess, target) {
    return {
        image: guess.id === target.id ? 'correct' : 'absent',
        name: guess.name === target.name ? 'correct' : 'absent',
        type: guess.type === target.type ? 'correct' : 'absent',
        health: guess.health === target.health ? 'correct' : 'absent',
        speed: guess.speed === target.speed ? 'correct' : 'absent',
        special: arraysEqual(guess.special, target.special) ? 'correct' :
                 guess.special.some(s => target.special.includes(s)) ? 'present' : 'absent'
    };
}

function arraysEqual(a, b) {
    if (a.length !== b.length) return false;
    return a.every(val => b.includes(val)) && b.every(val => a.includes(val));
}

function findEntityById(id) {
    return towers.find(t => t.id === id) ||
           heroes.find(h => h.id === id) ||
           bloons.find(b => b.id === id) ||
           moabs.find(m => m.id === id) ||
           bosses.find(b => b.id === id);
}

function setupBloonInput() {
    const input = document.getElementById('bloon-input');
    const suggestions = document.getElementById('bloon-suggestions');

    if (!input || !suggestions) return;

    const allBloons = [...bloons, ...moabs, ...bosses];

    input.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();

        if (query.length === 0) {
            suggestions.innerHTML = '';
            return;
        }

        const filtered = allBloons.filter(bloon =>
            bloon.name.toLowerCase().includes(query)
        ).slice(0, 6);

        suggestions.innerHTML = filtered.map(bloon => `
            <button onclick="makeGuessFromInput('${bloon.id}')"
                    class="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-left flex items-center gap-3 mb-2">
                <img src="${getImagePath(bloon)}" alt="${bloon.name}" class="w-8 h-8">
                <div>
                    <div class="font-medium">${bloon.name}</div>
                    <div class="text-sm opacity-75">${bloon.type} • ${bloon.health} HP</div>
                </div>
            </button>
        `).join('');
    });
}

function makeGuessFromInput(id) {
    makeGuess(id);
    document.getElementById('bloon-input').value = '';
    document.getElementById('bloon-suggestions').innerHTML = '';
}

function goBackToMenu() {
    showMainMenu();
}

function saveGame(mode) {
    localStorage.setItem(`bloonsdle-${mode}`, JSON.stringify(gameData[mode]));
}

function updateMenuStatus() {
    // Update menu status indicators
    ['classic', 'quote', 'upgrade', 'emoji', 'image', 'bloons'].forEach(mode => {
        const element = document.getElementById(`${mode}-status`);
        if (!element) return;
        
        const saved = localStorage.getItem(`bloonsdle-${mode}`);
        if (saved) {
            const game = JSON.parse(saved);
            const today = new Date().toISOString().split('T')[0];
            
            if (game.date === today) {
                if (game.status === 'won') {
                    element.className = 'game-mode-status completed';
                    element.innerHTML = '<span class="status-text">✓ Completado</span>';
                } else if (game.status === 'lost') {
                    element.className = 'game-mode-status failed';
                    element.innerHTML = '<span class="status-text">✗ Fallido</span>';
                } else {
                    element.className = 'game-mode-status';
                    element.innerHTML = `<span class="status-text">${game.guesses.length}/${game.maxGuesses}</span>`;
                }
                return;
            }
        }
        
        element.className = 'game-mode-status';
        element.innerHTML = '<span class="status-text">Jugar</span>';
    });
}

// Helper functions for classic mode
function renderClassicGuesses() {
    const game = gameData[currentMode];
    const container = document.getElementById('classic-guesses');
    if (!container) return;

    container.innerHTML = '';

    game.guesses.forEach(guess => {
        const guessEntity = findEntityById(guess.id);
        if (!guessEntity) return;

        const row = document.createElement('div');
        row.className = 'grid grid-cols-9 gap-1 mb-1 guess-row';

        const feedback = generateClassicFeedback(guessEntity, game.target);

        row.innerHTML = `
            <div class="p-2 flex items-center justify-center rounded game-tile ${feedback.image}">
                <img src="${getImagePath(guessEntity)}" alt="${guessEntity.name}" class="w-8 h-8">
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.name}">
                ${guessEntity.name}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.category}">
                ${guessEntity.category || 'N/A'}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.cost}">
                $${guessEntity.cost || 'N/A'}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.damage}">
                ${guessEntity.damage || 'N/A'}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.range}">
                ${guessEntity.range || 'N/A'}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.attackSpeed}">
                ${guessEntity.attackSpeed || 'N/A'}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.piercing}">
                ${guessEntity.piercing || 'N/A'}
            </div>
            <div class="p-2 text-center text-xs font-medium rounded game-tile ${feedback.special}">
                ${guessEntity.special?.join(', ') || 'None'}
            </div>
        `;

        container.appendChild(row);
    });
}

function generateClassicFeedback(guess, target) {
    return {
        image: guess.id === target.id ? 'correct' : 'absent',
        name: guess.name === target.name ? 'correct' : 'absent',
        category: guess.category === target.category ? 'correct' : 'absent',
        cost: guess.cost === target.cost ? 'correct' : 'absent',
        damage: guess.damage === target.damage ? 'correct' : 'absent',
        range: guess.range === target.range ? 'correct' : 'absent',
        attackSpeed: guess.attackSpeed === target.attackSpeed ? 'correct' : 'absent',
        piercing: guess.piercing === target.piercing ? 'correct' : 'absent',
        special: arraysEqual(guess.special || [], target.special || []) ? 'correct' :
                 (guess.special || []).some(s => (target.special || []).includes(s)) ? 'present' : 'absent'
    };
}

function setupTowerInput() {
    const input = document.getElementById('tower-input');
    const suggestions = document.getElementById('tower-suggestions');

    if (!input || !suggestions) return;

    const allEntities = [...towers, ...heroes];

    input.addEventListener('input', (e) => {
        const query = e.target.value.toLowerCase();

        if (query.length === 0) {
            suggestions.innerHTML = '';
            return;
        }

        const filtered = allEntities.filter(entity =>
            entity.name.toLowerCase().includes(query)
        ).slice(0, 6);

        suggestions.innerHTML = filtered.map(entity => `
            <button onclick="makeGuessFromInput('${entity.id}')"
                    class="w-full p-3 bg-white/10 rounded-lg hover:bg-white/20 text-white transition-colors text-left flex items-center gap-3 mb-2">
                <img src="${getImagePath(entity)}" alt="${entity.name}" class="w-8 h-8">
                <div>
                    <div class="font-medium">${entity.name}</div>
                    <div class="text-sm opacity-75">${entity.category || 'Hero'} • $${entity.cost || 'N/A'}</div>
                </div>
            </button>
        `).join('');
    });
}

function showGlobalStats() {
    alert('Estadísticas globales - En desarrollo');
}

function showHelp() {
    document.getElementById('help-modal').classList.remove('hidden');
    document.getElementById('help-content').innerHTML = `
        <p><strong>Objetivo:</strong> Completa todos los modos de juego diarios.</p>
        <p><strong>Clásico:</strong> Adivina la torre por sus propiedades.</p>
        <p><strong>Frase:</strong> Adivina el héroe por su frase característica.</p>
        <p><strong>Emoji:</strong> Adivina la torre representada por emojis.</p>
    `;
}

function hideHelp() {
    document.getElementById('help-modal').classList.add('hidden');
}

function hideGlobalStats() {
    document.getElementById('stats-modal').classList.add('hidden');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initApp);
