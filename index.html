<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bloonsdle - Daily BTD 6 Game</title>
    <link rel="icon" type="image/png" href="images/bloons/redBloon.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bloons-blue': '#4A90E2',
                        'bloons-green': '#7ED321',
                        'bloons-red': '#D0021B',
                        'bloons-yellow': '#F5A623',
                        'bloons-purple': '#9013FE',
                        'bloons-pink': '#FF69B4',
                        'bloons-black': '#1A1A1A',
                        'bloons-white': '#FFFFFF',
                        'bloons-gray': '#9B9B9B',
                        'bloons-dark-blue': '#2C5282',
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen">
    <!-- Language Selector -->
    <div class="language-selector">
        <select id="language-select" class="language-btn">
            <option value="es">🇪🇸 Español</option>
            <option value="en">🇺🇸 English</option>
        </select>
    </div>



    <!-- Main Menu Screen -->
    <div id="main-menu" class="min-h-screen flex flex-col items-center justify-center p-4">
        <!-- Logo and Title -->
        <div class="text-center mb-8 md:mb-12 px-4">
            <img src="images/tituloSinBG.png" alt="BLOONSDLE" class="mx-auto mb-4 max-w-xs md:max-w-md w-full h-auto title-image">
            <p class="text-white/80 text-lg md:text-xl" data-translate="subtitle">Daily Bloons TD 6 Guessing Games</p>

            <!-- Timer -->
            <div class="mt-4 md:mt-6 bg-black/30 rounded-lg p-3 md:p-4 max-w-xs md:max-w-sm mx-auto">
                <div class="text-white/70 text-xs md:text-sm mb-1" data-translate="next-reset">Próximo reset en:</div>
                <div id="countdown-timer" class="text-white font-mono text-base md:text-lg">--:--:--</div>
            </div>
        </div>

        <!-- Game Mode Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 max-w-6xl w-full px-4">
            <!-- Classic Mode -->
            <div class="game-mode-card" onclick="startGame('classic')">
                <div class="game-mode-icon">🎯</div>
                <h3 class="game-mode-title" data-translate="classic-title">Clásico</h3>
                <p class="game-mode-description" data-translate="classic-desc">Adivina la torre o el héroe por sus propiedades</p>
                <div class="game-mode-status" id="classic-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Quote Mode -->
            <div class="game-mode-card" onclick="startGame('quote')">
                <div class="game-mode-icon">💬</div>
                <h3 class="game-mode-title" data-translate="quote-title">Frase</h3>
                <p class="game-mode-description" data-translate="quote-desc">Adivina el héroe por su frase</p>
                <div class="game-mode-status" id="quote-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Upgrade Mode -->
            <div class="game-mode-card" onclick="startGame('upgrade')">
                <div class="game-mode-icon">⬆️</div>
                <h3 class="game-mode-title" data-translate="upgrade-title">Mejora</h3>
                <p class="game-mode-description" data-translate="upgrade-desc">Adivina la torre por su mejora</p>
                <div class="game-mode-status" id="upgrade-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Emoji Mode -->
            <div class="game-mode-card" onclick="startGame('emoji')">
                <div class="game-mode-icon">😄</div>
                <h3 class="game-mode-title" data-translate="emoji-title">Emoji</h3>
                <p class="game-mode-description" data-translate="emoji-desc">Adivina la torre o el héroe por emojis</p>
                <div class="game-mode-status" id="emoji-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Image Mode -->
            <div class="game-mode-card" onclick="startGame('image')">
                <div class="game-mode-icon">🖼️</div>
                <h3 class="game-mode-title" data-translate="image-title">Imagen</h3>
                <p class="game-mode-description" data-translate="image-desc">Adivina por la imagen</p>
                <div class="game-mode-status" id="image-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Bloons Mode -->
            <div class="game-mode-card" onclick="startGame('bloons')">
                <div class="game-mode-icon">🎈</div>
                <h3 class="game-mode-title" data-translate="bloons-title">Globos</h3>
                <p class="game-mode-description" data-translate="bloons-desc">Adivina el globo por sus propiedades</p>
                <div class="game-mode-status" id="bloons-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>


        </div>

        <!-- Buttons -->
        <div class="text-center mt-6 md:mt-8 space-y-4 px-4">
            <button onclick="showPatchNotes()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm" data-translate="patch-notes">
                Notas del parche
            </button>
            <div class="text-white/50 text-xs mt-4">v0.5</div>
        </div>

        <!-- Footer -->
        <div class="mt-6 md:mt-8 text-center text-white/60 text-xs md:text-sm px-4">
            <p class="mb-2"><span data-translate="made-by">Hecho por</span> <strong>@AlbertoSB00</strong> <span data-translate="for-fans">para fans de Bloons TD 6</span></p>
            <div class="flex justify-center gap-4 mb-2">
                <a href="https://github.com/AlbertoSB00" target="_blank" rel="noopener noreferrer" class="text-white/60 hover:text-white transition-colors" title="GitHub">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                </a>
                <a href="https://www.linkedin.com/in/appberto/" target="_blank" rel="noopener noreferrer" class="text-white/60 hover:text-white transition-colors" title="LinkedIn">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                </a>
                <a href="https://x.com/SirTychus" target="_blank" rel="noopener noreferrer" class="text-white/60 hover:text-white transition-colors" title="X (Twitter)">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>

    <!-- Game Screen (Hidden by default) -->
    <div id="game-screen" class="min-h-screen flex flex-col hidden">
        <!-- Game Header -->
        <header class="w-full max-w-4xl mx-auto p-3 md:p-4">
            <div class="flex items-center justify-between">
                <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="goBackToMenu()">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </button>

                <div class="flex items-center justify-center" id="game-title">
                    <img src="images/tituloSinBG.png" alt="BLOONSDLE" class="h-12 md:h-20 max-w-48 md:max-w-96 title-image-small">
                </div>

                <div class="flex gap-2">
                    <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="showHelp()">
                        <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="text-center mt-2">
                <p class="text-white/80 text-xs md:text-sm" id="game-subtitle">
                    Modo de juego
                </p>
            </div>
        </header>

        <!-- Game Content Container -->
        <div id="game-content" class="flex-1 flex flex-col items-center justify-start px-3 md:px-4 pb-6 md:pb-8">
            <!-- Content will be dynamically loaded based on game mode -->
        </div>
    </div>

    <!-- Help Modal -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 modal-backdrop flex items-center justify-center p-3 md:p-4 hidden">
        <div class="bg-white rounded-lg p-4 md:p-6 max-w-md w-full max-h-[90vh] overflow-y-auto relative">
            <!-- Close button (X) - Fixed position -->
            <button onclick="hideHelp()" class="absolute top-3 right-3 w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors z-10">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <h2 class="text-lg md:text-xl font-bold mb-3 md:mb-4 pr-10">Cómo Jugar</h2>
            <div id="help-content" class="space-y-2 md:space-y-3 text-xs md:text-sm">
                <!-- Help content will be dynamically loaded -->
            </div>
            <button onclick="hideHelp()" class="mt-3 md:mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                Entendido
            </button>
        </div>
    </div>



    <!-- Patch Notes Modal -->
    <div id="patch-notes-modal" class="fixed inset-0 bg-black/50 modal-backdrop flex items-center justify-center p-3 md:p-4 hidden">
        <div class="bg-white rounded-lg p-4 md:p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto relative">
            <!-- Close button (X) - Fixed position -->
            <button onclick="hidePatchNotes()" class="absolute top-3 right-3 w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors z-10">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <h2 class="text-lg md:text-xl font-bold mb-3 md:mb-4 pr-10" data-translate="patch-notes">Notas del parche</h2>
            <div id="patch-notes-content">
                <!-- Patch notes content will be dynamically loaded -->
            </div>
            <button onclick="hidePatchNotes()" class="mt-3 md:mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                Cerrar
            </button>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
