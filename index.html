<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bloonsdle - Daily Bloons TD 6 Guessing Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bloons-blue': '#4A90E2',
                        'bloons-green': '#7ED321',
                        'bloons-red': '#D0021B',
                        'bloons-yellow': '#F5A623',
                        'bloons-purple': '#9013FE',
                        'bloons-pink': '#FF69B4',
                        'bloons-black': '#1A1A1A',
                        'bloons-white': '#FFFFFF',
                        'bloons-gray': '#9B9B9B',
                        'bloons-dark-blue': '#2C5282',
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-500 to-blue-800">
    <!-- Main Menu Screen -->
    <div id="main-menu" class="min-h-screen flex flex-col items-center justify-center p-4">
        <!-- Logo and Title -->
        <div class="text-center mb-12">
            <h1 class="text-6xl font-bold text-white mb-4">BLOONSDLE</h1>
            <p class="text-white/80 text-xl">Daily Bloons TD 6 Guessing Games</p>
        </div>

        <!-- Game Mode Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl w-full">
            <!-- Classic Mode -->
            <div class="game-mode-card" onclick="startGame('classic')">
                <div class="game-mode-icon">🎯</div>
                <h3 class="game-mode-title">Clásico</h3>
                <p class="game-mode-description">Adivina la torre por sus propiedades</p>
                <div class="game-mode-status" id="classic-status">
                    <span class="status-text">Jugar</span>
                </div>
            </div>

            <!-- Quote Mode -->
            <div class="game-mode-card" onclick="startGame('quote')">
                <div class="game-mode-icon">💬</div>
                <h3 class="game-mode-title">Frase</h3>
                <p class="game-mode-description">Adivina el héroe por su frase</p>
                <div class="game-mode-status" id="quote-status">
                    <span class="status-text">Jugar</span>
                </div>
            </div>

            <!-- Upgrade Mode -->
            <div class="game-mode-card" onclick="startGame('upgrade')">
                <div class="game-mode-icon">⬆️</div>
                <h3 class="game-mode-title">Mejora</h3>
                <p class="game-mode-description">Adivina la torre por su mejora</p>
                <div class="game-mode-status" id="upgrade-status">
                    <span class="status-text">Jugar</span>
                </div>
            </div>

            <!-- Emoji Mode -->
            <div class="game-mode-card" onclick="startGame('emoji')">
                <div class="game-mode-icon">😄</div>
                <h3 class="game-mode-title">Emoji</h3>
                <p class="game-mode-description">Adivina la torre por emojis</p>
                <div class="game-mode-status" id="emoji-status">
                    <span class="status-text">Jugar</span>
                </div>
            </div>

            <!-- Image Mode -->
            <div class="game-mode-card" onclick="startGame('image')">
                <div class="game-mode-icon">🖼️</div>
                <h3 class="game-mode-title">Imagen</h3>
                <p class="game-mode-description">Adivina la torre por su imagen</p>
                <div class="game-mode-status" id="image-status">
                    <span class="status-text">Jugar</span>
                </div>
            </div>

            <!-- Stats Card -->
            <div class="game-mode-card" onclick="showGlobalStats()">
                <div class="game-mode-icon">📊</div>
                <h3 class="game-mode-title">Estadísticas</h3>
                <p class="game-mode-description">Ver tu progreso</p>
                <div class="game-mode-status">
                    <span class="status-text">Ver</span>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="mt-12 text-center text-white/60 text-sm">
            <p>Inspirado en Wordle • Hecho para fans de Bloons TD 6</p>
        </div>
    </div>

    <!-- Game Screen (Hidden by default) -->
    <div id="game-screen" class="min-h-screen flex flex-col hidden">
        <!-- Game Header -->
        <header class="w-full max-w-lg mx-auto p-4">
            <div class="flex items-center justify-between">
                <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="goBackToMenu()">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </button>

                <h1 class="text-2xl font-bold text-white text-center" id="game-title">
                    BLOONSDLE
                </h1>

                <div class="flex gap-2">
                    <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="showHelp()">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="text-center mt-2">
                <p class="text-white/80 text-sm" id="game-subtitle">
                    Modo de juego
                </p>
            </div>
        </header>

        <!-- Game Content Container -->
        <div id="game-content" class="flex-1 flex flex-col items-center justify-start px-4 pb-8">
            <!-- Content will be dynamically loaded based on game mode -->
        </div>
    </div>

    <!-- Help Modal -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 modal-backdrop flex items-center justify-center p-4 hidden">
        <div class="bg-white rounded-lg p-6 max-w-md w-full max-h-96 overflow-y-auto">
            <h2 class="text-xl font-bold mb-4">Cómo Jugar</h2>
            <div id="help-content" class="space-y-3 text-sm">
                <!-- Help content will be dynamically loaded -->
            </div>
            <button onclick="hideHelp()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                Entendido
            </button>
        </div>
    </div>

    <!-- Global Stats Modal -->
    <div id="stats-modal" class="fixed inset-0 bg-black/50 modal-backdrop flex items-center justify-center p-4 hidden">
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full max-h-96 overflow-y-auto">
            <h2 class="text-xl font-bold mb-4">Estadísticas Globales</h2>
            <div id="global-stats-content">
                <!-- Global stats content will be dynamically loaded -->
            </div>
            <button onclick="hideGlobalStats()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                Cerrar
            </button>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
