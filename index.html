<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bloonsdle - Daily Bloons TD 6 Guessing Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bloons-blue': '#4A90E2',
                        'bloons-green': '#7ED321',
                        'bloons-red': '#D0021B',
                        'bloons-yellow': '#F5A623',
                        'bloons-purple': '#9013FE',
                        'bloons-pink': '#FF69B4',
                        'bloons-black': '#1A1A1A',
                        'bloons-white': '#FFFFFF',
                        'bloons-gray': '#9B9B9B',
                        'bloons-dark-blue': '#2C5282',
                    }
                }
            }
        }
    </script>
    <style>
        .game-tile {
            @apply w-14 h-14 border-2 border-gray-300 flex items-center justify-center text-xs font-bold rounded-md transition-all duration-300;
        }
        
        .game-tile.filled {
            @apply border-gray-400 bg-white;
        }
        
        .game-tile.correct {
            background-color: #7ED321;
            border-color: #7ED321;
            color: white;
        }
        
        .game-tile.present {
            background-color: #F5A623;
            border-color: #F5A623;
            color: white;
        }
        
        .game-tile.absent {
            background-color: #9B9B9B;
            border-color: #9B9B9B;
            color: white;
        }
        
        .game-tile.higher {
            background-color: #D0021B;
            border-color: #D0021B;
            color: white;
        }
        
        .game-tile.lower {
            background-color: #4A90E2;
            border-color: #4A90E2;
            color: white;
        }
        
        .tower-image {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #4A90E2, #2C5282);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-500 to-blue-800">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="w-full max-w-lg mx-auto p-4">
            <div class="flex items-center justify-between">
                <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="showHelp()">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
                
                <h1 class="text-3xl font-bold text-white text-center">
                    BLOONSDLE
                </h1>
                
                <div class="flex gap-2">
                    <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="showStats()">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="text-center mt-2">
                <p class="text-white/80 text-sm">
                    ¡Adivina la torre de Bloons TD 6 en 6 intentos!
                </p>
            </div>
        </header>

        <!-- Game Content -->
        <div class="flex-1 flex flex-col items-center justify-start px-4 pb-8">
            <!-- Game Grid -->
            <div class="w-full max-w-4xl mx-auto p-4">
                <div class="overflow-x-auto">
                    <div class="min-w-full">
                        <!-- Header -->
                        <div class="grid grid-cols-9 gap-1 mb-2 text-xs font-semibold text-white">
                            <div class="p-2 text-center">Imagen</div>
                            <div class="p-2 text-center">Nombre</div>
                            <div class="p-2 text-center">Categoría</div>
                            <div class="p-2 text-center">Costo</div>
                            <div class="p-2 text-center">Daño</div>
                            <div class="p-2 text-center">Rango</div>
                            <div class="p-2 text-center">Velocidad</div>
                            <div class="p-2 text-center">Penetración</div>
                            <div class="p-2 text-center">Especial</div>
                        </div>

                        <!-- Guesses Grid -->
                        <div id="guesses-grid">
                            <!-- Guesses will be added here dynamically -->
                        </div>

                        <!-- Empty rows -->
                        <div id="empty-rows">
                            <!-- Empty rows will be generated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tower Search -->
            <div class="mt-8 w-full max-w-md" id="search-container">
                <div class="relative">
                    <input 
                        type="text" 
                        id="tower-search"
                        placeholder="Escribe el nombre de una torre..."
                        class="w-full px-4 py-3 text-lg border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none"
                        autocomplete="off"
                    />
                    <div id="search-results" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-64 overflow-y-auto hidden">
                        <!-- Search results will appear here -->
                    </div>
                </div>
            </div>

            <!-- Game End Message -->
            <div id="game-end-message" class="mt-8 text-center hidden">
                <!-- Game end message will appear here -->
            </div>

            <!-- Stats -->
            <div id="stats-display" class="mt-8 bg-white/10 rounded-lg p-6 text-white text-center max-w-md w-full hidden">
                <!-- Stats will appear here -->
            </div>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 hidden">
        <div class="bg-white rounded-lg p-6 max-w-md w-full max-h-96 overflow-y-auto">
            <h2 class="text-xl font-bold mb-4">Cómo Jugar Bloonsdle</h2>
            <div class="space-y-3 text-sm">
                <p><strong>Objetivo:</strong> Adivina la torre diaria de Bloons TD 6 en 6 intentos o menos.</p>
                <p><strong>Pistas:</strong> Después de cada intento, obtienes pistas sobre las propiedades de la torre:</p>
                <ul class="list-disc list-inside space-y-1 ml-4">
                    <li><span class="inline-block w-4 h-4 bg-green-500 rounded mr-2"></span>Verde: Propiedad correcta</li>
                    <li><span class="inline-block w-4 h-4 bg-yellow-500 rounded mr-2"></span>Amarillo: Coincidencia parcial</li>
                    <li><span class="inline-block w-4 h-4 bg-red-500 rounded mr-2"></span>Rojo: Valor mayor al objetivo</li>
                    <li><span class="inline-block w-4 h-4 bg-blue-500 rounded mr-2"></span>Azul: Valor menor al objetivo</li>
                    <li><span class="inline-block w-4 h-4 bg-gray-500 rounded mr-2"></span>Gris: No presente en la torre objetivo</li>
                </ul>
            </div>
            <button onclick="hideHelp()" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                Entendido
            </button>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
