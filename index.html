<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bloonsdle - Daily BTD 6 Game</title>
    <link rel="icon" type="image/png" href="images/bloons/redBloon.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'bloons-blue': '#4A90E2',
                        'bloons-green': '#7ED321',
                        'bloons-red': '#D0021B',
                        'bloons-yellow': '#F5A623',
                        'bloons-purple': '#9013FE',
                        'bloons-pink': '#FF69B4',
                        'bloons-black': '#1A1A1A',
                        'bloons-white': '#FFFFFF',
                        'bloons-gray': '#9B9B9B',
                        'bloons-dark-blue': '#2C5282',
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen">
    <!-- Language Selector -->
    <div class="language-selector">
        <select id="language-select" class="language-btn">
            <option value="es">🇪🇸 Español</option>
            <option value="en">🇺🇸 English</option>
        </select>
    </div>



    <!-- Main Menu Screen -->
    <div id="main-menu" class="min-h-screen flex flex-col items-center justify-center p-4">
        <!-- Logo and Title -->
        <div class="text-center mb-8 md:mb-12 px-4">
            <img src="images/tituloSinBG.png" alt="BLOONSDLE" class="mx-auto mb-4 max-w-xs md:max-w-md w-full h-auto title-image">
            <p class="text-white/80 text-lg md:text-xl" data-translate="subtitle">Daily Bloons TD 6 Guessing Games</p>

            <!-- Timer -->
            <div class="mt-4 md:mt-6 bg-black/30 rounded-lg p-3 md:p-4 max-w-xs md:max-w-sm mx-auto">
                <div class="text-white/70 text-xs md:text-sm mb-1" data-translate="next-reset">Próximo reset en:</div>
                <div id="countdown-timer" class="text-white font-mono text-base md:text-lg">--:--:--</div>
            </div>
        </div>

        <!-- Game Mode Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 max-w-6xl w-full px-4">
            <!-- Classic Mode -->
            <div class="game-mode-card" onclick="startGame('classic')">
                <div class="game-mode-icon">🎯</div>
                <h3 class="game-mode-title" data-translate="classic-title">Clásico</h3>
                <p class="game-mode-description" data-translate="classic-desc">Adivina la torre o el héroe por sus propiedades</p>
                <div class="game-mode-status" id="classic-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Quote Mode -->
            <div class="game-mode-card" onclick="startGame('quote')">
                <div class="game-mode-icon">💬</div>
                <h3 class="game-mode-title" data-translate="quote-title">Frase</h3>
                <p class="game-mode-description" data-translate="quote-desc">Adivina el héroe por su frase</p>
                <div class="game-mode-status" id="quote-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Upgrade Mode -->
            <div class="game-mode-card" onclick="startGame('upgrade')">
                <div class="game-mode-icon">⬆️</div>
                <h3 class="game-mode-title" data-translate="upgrade-title">Mejora</h3>
                <p class="game-mode-description" data-translate="upgrade-desc">Adivina la torre por su mejora</p>
                <div class="game-mode-status" id="upgrade-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Emoji Mode -->
            <div class="game-mode-card" onclick="startGame('emoji')">
                <div class="game-mode-icon">😄</div>
                <h3 class="game-mode-title" data-translate="emoji-title">Emoji</h3>
                <p class="game-mode-description" data-translate="emoji-desc">Adivina la torre o el héroe por emojis</p>
                <div class="game-mode-status" id="emoji-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Image Mode -->
            <div class="game-mode-card" onclick="startGame('image')">
                <div class="game-mode-icon">🖼️</div>
                <h3 class="game-mode-title" data-translate="image-title">Imagen</h3>
                <p class="game-mode-description" data-translate="image-desc">Adivina por la imagen</p>
                <div class="game-mode-status" id="image-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>

            <!-- Bloons Mode -->
            <div class="game-mode-card" onclick="startGame('bloons')">
                <div class="game-mode-icon">🎈</div>
                <h3 class="game-mode-title" data-translate="bloons-title">Globos</h3>
                <p class="game-mode-description" data-translate="bloons-desc">Adivina el globo por sus propiedades</p>
                <div class="game-mode-status" id="bloons-status">
                    <span class="status-text" data-translate="play">Jugar</span>
                </div>
            </div>


        </div>

        <!-- Buttons -->
        <div class="text-center mt-6 md:mt-8 space-y-4 px-4">
            <button onclick="showPatchNotes()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm" data-translate="patch-notes">
                Notas del parche
            </button>
            <div class="text-white/50 text-xs mt-4">v0.2</div>
        </div>

        <!-- Footer -->
        <div class="mt-6 md:mt-8 text-center text-white/60 text-xs md:text-sm px-4">
            <p><span data-translate="made-by">Hecho por</span> <a href="https://github.com/AlbertoSB00" target="_blank" rel="noopener noreferrer">@AlbertoSB00</a> <span data-translate="for-fans">para fans de Bloons TD 6</span></p>
        </div>
    </div>

    <!-- Game Screen (Hidden by default) -->
    <div id="game-screen" class="min-h-screen flex flex-col hidden">
        <!-- Game Header -->
        <header class="w-full max-w-4xl mx-auto p-3 md:p-4">
            <div class="flex items-center justify-between">
                <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="goBackToMenu()">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </button>

                <div class="flex items-center justify-center" id="game-title">
                    <img src="images/tituloSinBG.png" alt="BLOONSDLE" class="h-12 md:h-20 max-w-48 md:max-w-96 title-image-small">
                </div>

                <div class="flex gap-2">
                    <button class="p-2 rounded-full hover:bg-white/10 transition-colors" onclick="showHelp()">
                        <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="text-center mt-2">
                <p class="text-white/80 text-xs md:text-sm" id="game-subtitle">
                    Modo de juego
                </p>
            </div>
        </header>

        <!-- Game Content Container -->
        <div id="game-content" class="flex-1 flex flex-col items-center justify-start px-3 md:px-4 pb-6 md:pb-8">
            <!-- Content will be dynamically loaded based on game mode -->
        </div>
    </div>

    <!-- Help Modal -->
    <div id="help-modal" class="fixed inset-0 bg-black/50 modal-backdrop flex items-center justify-center p-3 md:p-4 hidden">
        <div class="bg-white rounded-lg p-4 md:p-6 max-w-md w-full max-h-[90vh] overflow-y-auto">
            <h2 class="text-lg md:text-xl font-bold mb-3 md:mb-4">Cómo Jugar</h2>
            <div id="help-content" class="space-y-2 md:space-y-3 text-xs md:text-sm">
                <!-- Help content will be dynamically loaded -->
            </div>
            <button onclick="hideHelp()" class="mt-3 md:mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                Entendido
            </button>
        </div>
    </div>



    <!-- Patch Notes Modal -->
    <div id="patch-notes-modal" class="fixed inset-0 bg-black/50 modal-backdrop flex items-center justify-center p-3 md:p-4 hidden">
        <div class="bg-white rounded-lg p-4 md:p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <h2 class="text-lg md:text-xl font-bold mb-3 md:mb-4" data-translate="patch-notes">Notas del parche</h2>
            <div id="patch-notes-content">
                <!-- Patch notes content will be dynamically loaded -->
            </div>
            <button onclick="hidePatchNotes()" class="mt-3 md:mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
                Cerrar
            </button>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
